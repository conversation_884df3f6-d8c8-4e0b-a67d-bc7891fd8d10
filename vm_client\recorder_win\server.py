from waitress import serve
from listener import app
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            os.path.join(os.path.expanduser('~'), 'Downloads', 'record_save', 'server.log')
        )
    ]
)

# Create save directory if it doesn't exist
save_path = os.path.join(os.path.expanduser('~'), 'Downloads', 'record_save')
os.makedirs(save_path, exist_ok=True)

if __name__ == '__main__':
    logging.info("Starting Waitress server on 127.0.0.1:4000")
    serve(app, host='127.0.0.1', port=4000, threads=4)