# Recorder API Documentation

## Window Control APIs

### 1. Show Window

**Endpoint**: `POST http://localhost:3000/showWindow`

**Function**: Show recorder window from tray

**Request Example**:
```bash
curl -X POST http://localhost:3000/showWindow
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Window shown successfully"
}
```

### 2. Hide Window

**Endpoint**: `POST http://localhost:3000/hideWindow`

**Function**: Hide recorder window to tray

**Request Example**:
```bash
curl -X POST http://localhost:3000/hideWindow
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Window hidden to tray successfully"
}
```

## Recording Control APIs

### 3. Start Recording

**Endpoint**: `POST http://localhost:3000/startRecording`

**Function**: Start screen recording

**Request Body** (optional):
```json
{
    "sourceId": "screen:0:0",
    "title": "My Recording"
}
```

**Request Example**:
```bash
# Start recording with default settings
curl -X POST http://localhost:3000/startRecording

# Start recording with custom title
curl -X POST http://localhost:3000/startRecording \
  -H "Content-Type: application/json" \
  -d '{"title": "Demo Recording"}'
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Recording started successfully"
}
```

### 4. Stop Recording

**Endpoint**: `POST http://localhost:3000/stopRecording`

**Function**: Stop current recording

**Request Example**:
```bash
curl -X POST http://localhost:3000/stopRecording
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Stop recording command sent successfully"
}
```

### 5. Get Recording Status

**Endpoint**: `GET http://localhost:3000/getStatus`

**Function**: Get current recording status and time

**Request Example**:
```bash
curl -X GET http://localhost:3000/getStatus
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Recording: 45s"
}
```

### 6. Refresh Screen Sources

**Endpoint**: `POST http://localhost:3000/refreshSources`

**Function**: Refresh available screen sources

**Request Example**:
```bash
curl -X POST http://localhost:3000/refreshSources
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Sources refreshed successfully",
    "sources": [
        {
            "id": "screen:0:0",
            "name": "Screen 1 (main monitor)",
            "isPrimary": true,
            "bounds": {
                "x": 0,
                "y": 0,
                "width": 1920,
                "height": 1080
            }
        }
    ]
}
```

### 7. Get Latest Recording

**Endpoint**: `GET http://localhost:3000/getLatestRecording`

**Function**: Get path and info of the latest recorded video

**Request Example**:
```bash
curl -X GET http://localhost:3000/getLatestRecording
```

**Response Example**:
```json
{
    "status": "success",
    "message": "Latest recording found",
    "latestRecording": {
        "path": "C:\\Users\\<USER>\\Downloads\\record_save\\Demo-Recording-14-30-25.mkv",
        "filename": "Demo-Recording-14-30-25.mkv",
        "size": 15728640,
        "sizeFormatted": "15.00 MB",
        "created": "2024-01-20T14:30:25.000Z",
        "modified": "2024-01-20T14:32:10.000Z"
    },
    "allRecordings": [...]
}
```

### 8. Get Recorded File Path (Legacy)

**Endpoint**: `GET http://localhost:3000/getRecordedFilePath`

**Function**: Get path of the last recorded file (legacy endpoint)

**Request Example**:
```bash
curl -X GET http://localhost:3000/getRecordedFilePath
```

**Response Example**:
```json
{
    "status": "success",
    "filePath": "C:\\Users\\<USER>\\Downloads\\record_save\\Recording-14-30-25.mkv"
}
```

## Usage Examples

### Complete Recording Workflow

```python
import requests
import time

BASE_URL = "http://localhost:3000"

# 1. Show the recorder window
response = requests.post(f"{BASE_URL}/showWindow")
print("Show window:", response.json())

# 2. Refresh sources to get latest screen info
response = requests.post(f"{BASE_URL}/refreshSources")
sources = response.json().get('sources', [])
print("Available sources:", len(sources))

# 3. Start recording with custom title
response = requests.post(f"{BASE_URL}/startRecording",
                        json={"title": "API Demo Recording"})
print("Start recording:", response.json())

# 4. Wait for some recording time
time.sleep(10)

# 5. Check recording status
response = requests.get(f"{BASE_URL}/getStatus")
print("Status:", response.json())

# 6. Stop recording
response = requests.post(f"{BASE_URL}/stopRecording")
print("Stop recording:", response.json())

# 7. Get the latest recording info
time.sleep(2)  # Wait for file to be saved
response = requests.get(f"{BASE_URL}/getLatestRecording")
recording_info = response.json()
print("Latest recording:", recording_info.get('latestRecording', {}).get('filename'))

# 8. Hide window back to tray
response = requests.post(f"{BASE_URL}/hideWindow")
print("Hide window:", response.json())
```

### Command Line Usage

```bash
# Complete workflow using curl
echo "Starting recording workflow..."

# Show window
curl -s -X POST http://localhost:3000/showWindow

# Start recording
curl -s -X POST http://localhost:3000/startRecording \
  -H "Content-Type: application/json" \
  -d '{"title": "CLI Recording"}'

# Wait 10 seconds
sleep 10

# Check status
curl -s -X GET http://localhost:3000/getStatus

# Stop recording
curl -s -X POST http://localhost:3000/stopRecording

# Get latest recording
sleep 2
curl -s -X GET http://localhost:3000/getLatestRecording

# Hide window
curl -s -X POST http://localhost:3000/hideWindow

echo "Recording workflow completed!"
```

### Integration Example

```javascript
// JavaScript integration example
class RecorderAPI {
    constructor(baseUrl = 'http://localhost:3000') {
        this.baseUrl = baseUrl;
    }

    async showWindow() {
        const response = await fetch(`${this.baseUrl}/showWindow`, {
            method: 'POST'
        });
        return response.json();
    }

    async startRecording(title = null) {
        const body = title ? JSON.stringify({ title }) : undefined;
        const response = await fetch(`${this.baseUrl}/startRecording`, {
            method: 'POST',
            headers: title ? { 'Content-Type': 'application/json' } : {},
            body
        });
        return response.json();
    }

    async stopRecording() {
        const response = await fetch(`${this.baseUrl}/stopRecording`, {
            method: 'POST'
        });
        return response.json();
    }

    async getLatestRecording() {
        const response = await fetch(`${this.baseUrl}/getLatestRecording`);
        return response.json();
    }

    async hideWindow() {
        const response = await fetch(`${this.baseUrl}/hideWindow`, {
            method: 'POST'
        });
        return response.json();
    }
}

// Usage
const recorder = new RecorderAPI();
await recorder.showWindow();
await recorder.startRecording("My Recording");
// ... wait some time ...
await recorder.stopRecording();
const latest = await recorder.getLatestRecording();
console.log('Recorded:', latest.latestRecording.filename);
await recorder.hideWindow();
```

## Error Handling

Common error responses:

### Application Not Ready
```json
{
    "status": "error",
    "message": "Application window is not ready"
}
```

### Recording Already in Progress
```json
{
    "status": "error",
    "message": "Recording is already in progress"
}
```

### No Recording in Progress
```json
{
    "status": "error",
    "message": "No recording in progress"
}
```

### No Recordings Found
```json
{
    "status": "error",
    "message": "No recordings found"
}
```

## Testing

Test all APIs with the provided test script:
```bash
test_api.bat
```

Or test individual endpoints:
```bash
# Test window control
curl -X POST http://localhost:3000/showWindow
curl -X POST http://localhost:3000/hideWindow

# Test recording
curl -X POST http://localhost:3000/startRecording
curl -X GET http://localhost:3000/getStatus
curl -X POST http://localhost:3000/stopRecording
curl -X GET http://localhost:3000/getLatestRecording
```

## Notes

1. **Port**: Ensure recorder application is running on port 3000
2. **Permissions**: Windows may require firewall permissions
3. **File Location**: Recordings are saved to `~/Downloads/record_save/`
4. **File Format**: Videos are saved as `.mkv` files
5. **Concurrent Recording**: Only one recording session at a time
6. **Background Operation**: Application continues running in tray when hidden
