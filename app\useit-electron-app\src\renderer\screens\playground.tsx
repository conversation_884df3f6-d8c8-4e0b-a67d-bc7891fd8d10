import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { TitleBar } from 'renderer/components/title-bar';

// Access the App API
const { App } = window;

export function PlaygroundScreen() {
  const navigate = useNavigate();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [currentAppMode, setCurrentAppMode] = useState('Workflows');
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [iframeLoadTimeout, setIframeLoadTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 检查localhost:3001是否可用，增加超时时间和重试机制
    const checkConnection = async (retryCount = 0) => {
      try {
        const response = await fetch('http://localhost:3001/explore/apps', {
          mode: 'no-cors',
          signal: AbortSignal.timeout(30000) // 增加到30秒超时
        });
        setConnectionStatus('connected');
      } catch (error) {
        console.error(`Failed to connect to localhost:3001 (attempt ${retryCount + 1}):`, error);

        // 如果是前几次尝试失败，可能是前端还在编译，继续重试
        if (retryCount < 5) {
          console.log(`Retrying connection in 5 seconds... (attempt ${retryCount + 2}/6)`);
          setTimeout(() => checkConnection(retryCount + 1), 5000);
        } else {
          setConnectionStatus('failed');
        }
      }
    };

    checkConnection();
    const interval = setInterval(() => checkConnection(), 15000); // 每15秒检查一次

    // 设置iframe加载超时（60秒）
    const setupIframeTimeout = () => {
      const timeout = setTimeout(() => {
        console.warn('Iframe load timeout after 60 seconds');
        setConnectionStatus('failed');
      }, 60000);
      setIframeLoadTimeout(timeout);
    };

    // 当连接状态变为connected时，设置iframe超时
    if (connectionStatus === 'connected') {
      setupIframeTimeout();
    }

    // Handle messages from iframe (playground)
    const handleMessage = async (event: MessageEvent) => {
      // Verify origin for security (optional, adjust as needed)
      if (event.origin !== 'http://localhost:3001') {
        return;
      }

      if (event.data?.type === 'electron-api-call') {
        const { messageId, method, params } = event.data;
        
        try {
          let result;
          
          switch (method) {
            // VM Management APIs
            case 'getVmStatus':
              result = await App.getVmStatus(params?.vmName || params);
              break;
            case 'startVm':
              result = await App.startVm(params?.vmName || params);
              break;
            case 'stopVm':
              result = await App.stopVm(params?.vmName || params);
              break;
            case 'getVmIpAddress':
              result = await App.getVmIpAddress(params?.vmName || params);
              break;
            case 'createVm':
              result = await App.createVm();
              break;
            case 'enableHyperV':
              result = await App.enableHyperV();
              break;
            case 'repairWindowsComponents':
              result = await App.repairWindowsComponents();
              break;
            case 'getVmDetails':
              result = await App.getVmDetails(params?.vmName || params);
              break;

            // RDP/MSTSC APIs
            case 'getMstscStatus':
              result = await App.getMstscStatus();
              break;
            case 'getMstscUrl':
              result = await App.getMstscUrl(params);
              break;
            case 'startRdpSession':
              result = await App.startRdpSession(params);
              break;
            case 'stopRdpSession':
              result = await App.stopRdpSession(params);
              break;
            case 'openRdpView':
              result = await App.openRdpView(params);
              break;

            // Video Recording APIs
            case 'startRecording':
              result = await App.startRecording();
              break;
            case 'stopRecording':
              result = await App.stopRecording();
              break;
            case 'getRecordingStatus':
              result = await App.getRecordingStatus();
              break;
            case 'uploadRecording':
              result = await App.uploadRecording();
              break;
            case 'testVideoExe':
              result = await App.testVideoExe();
              break;
            case 'readRecordedFile':
              result = await App.readRecordedFile(params);
              break;

            // Diagnostic APIs
            case 'diagnoseVmEnvironment':
              result = await App.diagnoseVmEnvironment();
              break;

            // Local VM Action Execution
            case 'executeLocalVmAction':
              result = await App.executeLocalVmAction(params);
              break;

            // Check VM Port
            case 'checkVmPort':
              result = await App.checkVmPort(params);
              break;

            // Get Persistent RDP Session
            case 'getPersistentRdpSession':
              result = await App.getPersistentRdpSession(params);
              break;

            // Get RDP Sessions Status
            case 'getRdpSessionsStatus':
              result = await App.getRdpSessionsStatus();
              break;

            // AI Run Relay APIs
            case 'aiRunRelayStart':
              result = await App.aiRunRelayStart(params);
              break;
            case 'aiRunRelayMessage':
              console.log('[Playground] 🚀 Calling aiRunRelayMessage with params:', params);
              result = await App.aiRunRelayMessage(params);
              console.log('[Playground] 📥 aiRunRelayMessage result:', result);
              break;
            case 'aiRunRelayStop':
              result = await App.aiRunRelayStop(params);
              break;
            case 'aiRunRelayPause':
              result = await App.aiRunRelayPause(params);
              break;
            case 'aiRunRelayResume':
              result = await App.aiRunRelayResume(params);
              break;

            default:
              throw new Error(`Unknown method: ${method}`);
          }
          
          // Send response back to iframe
          (event.source as Window)?.postMessage({
            type: 'electron-api-response',
            messageId,
            success: true,
            result
          }, '*');
          
        } catch (error: any) {
          console.error(`[Playground] API call failed: ${method}`, error);
          
          // Send error response back to iframe
          (event.source as Window)?.postMessage({
            type: 'electron-api-response',
            messageId,
            success: false,
            error: error.message || 'Unknown error'
          }, '*');
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      clearInterval(interval);
      window.removeEventListener('message', handleMessage);
      // 清理iframe超时定时器
      if (iframeLoadTimeout) {
        clearTimeout(iframeLoadTimeout);
      }
    };
  }, []);

  const handleModeChange = (mode: string) => {
    console.log('Mode change requested:', mode);
    setCurrentAppMode(mode);
    if (mode === 'VMs') {
      // 切换回VMs模式 - 使用React Router的navigate
      console.log('Navigating back to VMs...');
      navigate('/vm-setup');
    }
  };

  // const titleBarHeight = 40; // Mac和Windows的标题栏高度 - 临时注释掉

  return (
    <div
      className="app-container window-frame h-screen w-screen flex flex-col"
      style={{
        margin: '0',
        padding: '0',
        border: 'none',
        outline: 'none',
        overflow: 'hidden'
      }}
    >
      {/* 简化的顶栏 */}
      <div
        style={{
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: '#222225', // playground的深灰色背景
          WebkitAppRegion: 'drag',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
        } as any}
      >
        {/* 左侧按钮组 */}
        <div style={{ display: 'flex', alignItems: 'center', paddingLeft: '12px', WebkitAppRegion: 'no-drag' } as any}>
          <button
            onClick={() => handleModeChange('VMs')}
            style={{
              background: 'transparent', // 移除背景色
              color: currentAppMode === 'VMs' ? '#f79009' : '#9ca3af', // playground的橙色高亮
              border: 'none',
              padding: '4px 12px',
              fontSize: '14px',
              cursor: 'pointer',
              borderRadius: '4px',
              marginRight: '8px',
              transition: 'all 0.2s ease',
              fontWeight: currentAppMode === 'VMs' ? '600' : '400' // 选中时加粗
            }}
            onMouseEnter={(e) => {
              if (currentAppMode !== 'VMs') {
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (currentAppMode !== 'VMs') {
                e.currentTarget.style.color = '#9ca3af';
              }
            }}
          >
            VMs
          </button>
          <button
            onClick={() => handleModeChange('Workflows')}
            style={{
              background: 'transparent', // 移除背景色
              color: currentAppMode === 'Workflows' ? '#f79009' : '#9ca3af', // playground的橙色高亮
              border: 'none',
              padding: '4px 12px',
              fontSize: '14px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'all 0.2s ease',
              fontWeight: currentAppMode === 'Workflows' ? '600' : '400' // 选中时加粗
            }}
            onMouseEnter={(e) => {
              if (currentAppMode !== 'Workflows') {
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (currentAppMode !== 'Workflows') {
                e.currentTarget.style.color = '#9ca3af';
              }
            }}
          >
            Workflows
          </button>
        </div>

        {/* 右侧窗口控制按钮 */}
        <div style={{ display: 'flex', height: '100%', WebkitAppRegion: 'no-drag' } as any}>
          <button
            style={{
              width: '46px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.1)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            onClick={() => window.App?.windowMinimize?.()}
            aria-label="Minimize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 6h8" stroke="#ffffff" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </button>
          <button
            style={{
              width: '46px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.1)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            onClick={() => window.App?.windowMaximize?.()}
            aria-label="Maximize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="2" width="8" height="8" stroke="#ffffff" strokeWidth="1.5" fill="none" strokeLinecap="round"/>
            </svg>
          </button>
          <button
            style={{
              width: '46px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              borderTopRightRadius: '16px',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#e81123';
              e.currentTarget.style.borderTopRightRadius = '16px';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderTopRightRadius = '16px';
            }}
            onClick={() => window.App?.windowClose?.()}
            aria-label="Close"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 3l6 6M3 9l6-6" stroke="#ffffff" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div
        className="flex-1 w-full bg-background"
        style={{
          marginTop: `32px`, // 恢复标题栏高度
          height: `calc(100vh - 32px)`, // 减去标题栏高度
          background: 'var(--background)',
          border: 'none',
          outline: 'none',
          marginLeft: '0',
          marginRight: '0',
          marginBottom: '0',
          padding: '0',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        {connectionStatus === 'failed' ? (
          <div className="flex items-center justify-center h-full bg-background text-foreground">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4 text-destructive">连接失败</h2>
              <p className="text-muted-foreground mb-4">
                无法连接到 localhost:3001
              </p>
              <p className="text-sm text-muted-foreground">
                请确保在 playground 目录运行了 'pnpm run dev'
              </p>
              <button
                className="mt-4 px-4 py-2 bg-orange-primary text-white rounded hover:bg-orange-primary/90 transition-colors"
                onClick={() => setConnectionStatus('connecting')}
              >
                重试连接
              </button>
            </div>
          </div>
        ) : connectionStatus === 'connecting' ? (
          <div className="flex items-center justify-center h-full bg-background text-foreground">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">正在连接到 Playground...</p>
            </div>
          </div>
        ) : (
          <iframe
            ref={iframeRef}
            src="http://localhost:3001/explore/apps"
            className="w-full h-full border-none"
            title="Playground"
            onLoad={() => {
              setIframeLoaded(true);
              // 清除超时定时器
              if (iframeLoadTimeout) {
                clearTimeout(iframeLoadTimeout);
                setIframeLoadTimeout(null);
              }
            }}
            onError={(e) => {
              console.error('Iframe load error:', e);
              setConnectionStatus('failed');
              // 清除超时定时器
              if (iframeLoadTimeout) {
                clearTimeout(iframeLoadTimeout);
                setIframeLoadTimeout(null);
              }
            }}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              outline: 'none',
              margin: '0',
              padding: '0',
              display: 'block'
            }}
          />
        )}
      </div>
    </div>
  );
}

export default PlaygroundScreen;