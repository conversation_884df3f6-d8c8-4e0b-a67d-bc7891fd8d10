# Recording Control APIs - Port 3000

All recording APIs are available on the main Electron app at `http://localhost:3000`. These APIs allow external applications to control the recorder without needing to interact with the Python backend directly.

## Available APIs

### 1. Start Recording
**Endpoint**: `POST /startRecording`

**Description**: Starts screen recording with optional parameters

**Request Body** (optional):
```json
{
    "sourceId": "screen:0:0",
    "title": "My Recording"
}
```

**Response**:
```json
{
    "status": "success",
    "message": "Recording started successfully"
}
```

**Example**:
```bash
# Start with default settings
curl -X POST http://localhost:3000/startRecording

# Start with custom title
curl -X POST http://localhost:3000/startRecording \
  -H "Content-Type: application/json" \
  -d '{"title": "Demo Recording"}'
```

### 2. Stop Recording
**Endpoint**: `POST /stopRecording`

**Description**: Stops the current recording

**Response**:
```json
{
    "status": "success",
    "message": "Stop recording command sent successfully"
}
```

**Example**:
```bash
curl -X POST http://localhost:3000/stopRecording
```

### 3. Get Recording Status
**Endpoint**: `GET /getStatus`

**Description**: Gets current recording status and duration

**Response**:
```json
{
    "status": "success",
    "message": "Recording: 45s"
}
```

**Example**:
```bash
curl -X GET http://localhost:3000/getStatus
```

### 4. Refresh Sources
**Endpoint**: `POST /refreshSources`

**Description**: Refreshes available screen sources

**Response**:
```json
{
    "status": "success",
    "message": "Sources refreshed successfully",
    "sources": [
        {
            "id": "screen:0:0",
            "name": "Entire Screen",
            "thumbnail": "data:image/png;base64,..."
        }
    ]
}
```

**Example**:
```bash
curl -X POST http://localhost:3000/refreshSources
```

### 5. Get Latest Recording
**Endpoint**: `GET /getLatestRecording`

**Description**: Gets information about the most recent recording

**Response**:
```json
{
    "status": "success",
    "message": "Latest recording found",
    "latestRecording": {
        "path": "C:\\Users\\<USER>\\Downloads\\record_save\\recording_20240720_143022.mkv",
        "filename": "recording_20240720_143022.mkv",
        "size": 15728640,
        "sizeFormatted": "15.0 MB",
        "created": "2024-07-20T14:30:22.000Z",
        "modified": "2024-07-20T14:32:15.000Z"
    },
    "allRecordings": [...]
}
```

**Example**:
```bash
curl -X GET http://localhost:3000/getLatestRecording
```

### 6. Get Recorded File Path
**Endpoint**: `GET /getRecordedFilePath`

**Description**: Gets the path of the most recently completed recording

**Response**:
```json
{
    "status": "success",
    "filePath": "C:\\Users\\<USER>\\Downloads\\record_save\\recording_20240720_143022.mkv"
}
```

**Example**:
```bash
curl -X GET http://localhost:3000/getRecordedFilePath
```

## Complete Workflow Example

```bash
#!/bin/bash

BASE_URL="http://localhost:3000"

echo "1. Checking status..."
curl -s -X GET $BASE_URL/getStatus

echo -e "\n2. Refreshing sources..."
curl -s -X POST $BASE_URL/refreshSources

echo -e "\n3. Starting recording..."
curl -s -X POST $BASE_URL/startRecording \
  -H "Content-Type: application/json" \
  -d '{"title": "API Demo Recording"}'

echo -e "\n4. Recording for 10 seconds..."
sleep 10

echo -e "\n5. Checking status during recording..."
curl -s -X GET $BASE_URL/getStatus

echo -e "\n6. Stopping recording..."
curl -s -X POST $BASE_URL/stopRecording

echo -e "\n7. Waiting for file to be saved..."
sleep 3

echo -e "\n8. Getting latest recording info..."
curl -s -X GET $BASE_URL/getLatestRecording

echo -e "\n9. Getting recorded file path..."
curl -s -X GET $BASE_URL/getRecordedFilePath
```

## Integration Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

class RecorderAPI {
    constructor(baseUrl = 'http://localhost:3000') {
        this.baseUrl = baseUrl;
    }

    async startRecording(title = null) {
        const data = title ? { title } : {};
        const response = await axios.post(`${this.baseUrl}/startRecording`, data);
        return response.data;
    }

    async stopRecording() {
        const response = await axios.post(`${this.baseUrl}/stopRecording`);
        return response.data;
    }

    async getStatus() {
        const response = await axios.get(`${this.baseUrl}/getStatus`);
        return response.data;
    }

    async getLatestRecording() {
        const response = await axios.get(`${this.baseUrl}/getLatestRecording`);
        return response.data;
    }
}

// Usage
const recorder = new RecorderAPI();
await recorder.startRecording('My Demo');
// ... wait some time
await recorder.stopRecording();
const latest = await recorder.getLatestRecording();
console.log('Recording saved to:', latest.latestRecording.path);
```

### Python
```python
import requests
import time

class RecorderAPI:
    def __init__(self, base_url='http://localhost:3000'):
        self.base_url = base_url
    
    def start_recording(self, title=None):
        data = {'title': title} if title else {}
        response = requests.post(f'{self.base_url}/startRecording', json=data)
        return response.json()
    
    def stop_recording(self):
        response = requests.post(f'{self.base_url}/stopRecording')
        return response.json()
    
    def get_status(self):
        response = requests.get(f'{self.base_url}/getStatus')
        return response.json()
    
    def get_latest_recording(self):
        response = requests.get(f'{self.base_url}/getLatestRecording')
        return response.json()

# Usage
recorder = RecorderAPI()
recorder.start_recording('Python Demo')
time.sleep(10)
recorder.stop_recording()
time.sleep(2)
latest = recorder.get_latest_recording()
print(f"Recording saved to: {latest['latestRecording']['path']}")
```

## Error Handling

All APIs return consistent error responses:

```json
{
    "status": "error",
    "message": "Error description"
}
```

Common error scenarios:
- Application not ready
- Recording already in progress (for start)
- No recording in progress (for stop)
- No recordings found
- File system errors

## Notes

- All recording files are saved to `~/Downloads/record_save/`
- Recordings are saved in MKV format
- The app must be running for APIs to work
- APIs work regardless of whether the window is visible or hidden
- Recording continues even if the window is hidden to tray
