@echo off
echo Building Recorder...

echo Cleaning...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist pnpm-lock.yaml del pnpm-lock.yaml

echo Installing dependencies...
npm install --force

echo Adding missing dependencies...
npm install es-errors ms bytes unpipe destroy raw-body iconv-lite --save

echo Building...
npm run build:win

if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo SUCCESS! Check release/ folder
echo.
echo Test APIs:
echo curl -X POST http://localhost:3000/showWindow
echo curl -X POST http://localhost:3000/hideWindow
echo.
pause
