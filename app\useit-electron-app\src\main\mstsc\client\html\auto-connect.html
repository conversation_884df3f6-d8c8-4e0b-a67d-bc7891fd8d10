<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="./img/favicon.ico">

    <title>UseIt RDP Connection</title>

    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom styles for this template -->
    <link href="./css/signin.css" rel="stylesheet">
    <script type="text/javascript" src="./socket.io/socket.io.js"></script>
	<script type="text/javascript" src="./js/mstsc.js"></script>
	<script type="text/javascript" src="./js/keyboard.js"></script>
	<script type="text/javascript" src="./js/rle.js"></script>
	<script type="text/javascript" src="./js/client.js"></script>
	<script type="text/javascript" src="./js/canvas.js"></script>
    <script language="javascript">
    var client = null;
    
    function load (canvas) {
    	client = Mstsc.client.create(Mstsc.$(canvas));
    	
    	// 从URL参数中获取连接信息，或使用默认值
    	var urlParams = new URLSearchParams(window.location.search);
    	var ip = urlParams.get('ip') || '**************';
    	var domain = urlParams.get('domain') || '';
    	var username = urlParams.get('username') || 'UseIt';
    	var password = urlParams.get('password') || '123456';
    	var sessionId = urlParams.get('sessionId') || 'default';

    	console.log('Auto-connecting to:', ip, 'with user:', username, 'sessionId:', sessionId);
    	console.log('URL params parsed:', { ip, domain, username, password: password ? '[HIDDEN]' : 'empty', sessionId });

    	// 显示连接状态
    	document.getElementById('status').innerHTML = 'Connecting to ' + ip + ' (Session: ' + sessionId + ')...';
    	document.getElementById('debug').innerHTML = 'Parameters: IP=' + ip + ', User=' + username + ', Domain=' + domain + ', SessionID=' + sessionId;

    	// 自动连接延迟1秒
    	setTimeout(function() {
    		console.log('Starting auto-connect with sessionId:', sessionId);
    		connect(ip, domain, username, password, sessionId);
    	}, 1000);
    }
    
	function connect (ip, domain, username, password, sessionId) {
		console.log('Connecting to RDP:', ip, domain, username, 'with sessionId:', sessionId);

		try {
			Mstsc.$("main").style.display = 'none';
			var canvas = Mstsc.$("myCanvas");
			canvas.style.display = 'inline';
			canvas.width = window.innerWidth;
			canvas.height = window.innerHeight;

			// 🎯 确保canvas可以接收键盘事件
			canvas.tabIndex = 0;
			canvas.style.outline = 'none';
			canvas.focus();
			console.log('[auto-connect] 🎯 Canvas configured for keyboard input');

			console.log('Canvas setup complete, initiating connection with session:', sessionId);

			client.connect(ip, domain, username, password, sessionId, function (err) {
				console.log('Connection callback called with:', err);
				if (err) {
					console.error('RDP Connection error:', err);
					Mstsc.$("myCanvas").style.display = 'none';
					Mstsc.$("main").style.display = 'inline';
					document.getElementById('status').innerHTML = 'Connection failed: ' + (err.message || err.code || err);
					document.getElementById('debug').innerHTML = 'Error details: ' + JSON.stringify(err);
				} else {
					console.log('RDP Connection closed normally');
					Mstsc.$("myCanvas").style.display = 'none';
					Mstsc.$("main").style.display = 'inline';
					document.getElementById('status').innerHTML = 'Connection closed';
					document.getElementById('debug').innerHTML = 'Connection terminated normally';
				}
			});

			// 🔄 监听连接建立事件，自动请求完整屏幕刷新
			if (client && client.socket) {
				client.socket.on('rdp-connect', function() {
					console.log('[auto-connect] 🔄 RDP connected, requesting full screen refresh for session reuse');

					// 延迟请求刷新，确保连接完全建立
					setTimeout(function() {
						if (client && client.render && typeof client.render.requestFullRefresh === 'function') {
							console.log('[auto-connect] 🔄 Requesting full screen refresh via canvas');
							client.render.requestFullRefresh();
						}

						// 备用方案：通过socket直接请求刷新
						if (client && client.socket && client.socket.connected) {
							console.log('[auto-connect] 🔄 Requesting screen refresh via socket');
							client.socket.emit('refresh-screen');
						}

						// 额外的canvas重绘请求
						if (client && client.render && typeof client.render.forceCanvasRecreation === 'function') {
							console.log('[auto-connect] 🔄 Force canvas recreation for session reuse');
							client.render.forceCanvasRecreation();
						}
					}, 1500); // 1.5秒延迟确保连接稳定

					// 再次延迟请求，处理可能的延迟加载
					setTimeout(function() {
						if (client && client.socket && client.socket.connected) {
							console.log('[auto-connect] 🔄 Secondary refresh request');
							client.socket.emit('refresh-screen');
						}
					}, 3000); // 3秒后再次请求
				});
			}

			// 🎯 连接后延迟聚焦canvas以确保键盘输入
			setTimeout(function() {
				canvas.focus();
				window.focus();
				console.log('[auto-connect] 🎯 Canvas focused after connection for keyboard input');
			}, 2000);

		} catch (error) {
			console.error('Exception during connect:', error);
			document.getElementById('status').innerHTML = 'Connection error: ' + error.message;
			document.getElementById('debug').innerHTML = 'Exception: ' + error.toString();
		}
	}
	
	// ESC键断开连接
	document.addEventListener('keydown', function(event) {
		if (event.keyCode === 27) { // ESC
			console.log('ESC pressed, disconnecting...');
			if (client && client.socket) {
				client.socket.disconnect();
			}
		}
	});

	// 🔄 监听来自父窗口的刷新请求
	window.addEventListener('message', function(event) {
		if (event.data && event.data.type === 'refresh-screen') {
			console.log('[auto-connect] 🔄 Received refresh request from parent window');

			if (client && client.render && typeof client.render.requestFullRefresh === 'function') {
				console.log('[auto-connect] 🔄 Executing full screen refresh');
				client.render.requestFullRefresh();
			} else if (client && client.socket && client.socket.connected) {
				console.log('[auto-connect] 🔄 Fallback: requesting refresh via socket');
				client.socket.emit('refresh-screen');
			} else {
				console.log('[auto-connect] ⚠️ Cannot refresh: client not ready');
			}
		}
	});

	// 🎯 添加焦点管理和键盘事件监听
	window.addEventListener('load', function() {
		console.log('Page loaded, mstsc client available:', typeof Mstsc !== 'undefined');

		// 确保页面加载后聚焦
		setTimeout(function() {
			window.focus();
			var canvas = document.getElementById('myCanvas');
			if (canvas) {
				canvas.focus();
				console.log('[auto-connect] 🎯 Initial focus set on page load');
			}
		}, 500);
	});

	// 🎯 点击页面任何地方都聚焦canvas
	document.addEventListener('click', function(event) {
		var canvas = document.getElementById('myCanvas');
		if (canvas && canvas.style.display !== 'none') {
			canvas.focus();
			console.log('[auto-connect] 🎯 Canvas focused on click');
		}
	});

	// 🎯 监听焦点事件
	window.addEventListener('focus', function() {
		console.log('[auto-connect] 🎯 Window focused - keyboard input enabled');
		var canvas = document.getElementById('myCanvas');
		if (canvas && canvas.style.display !== 'none') {
			canvas.focus();
		}
	});

	window.addEventListener('blur', function() {
		console.log('[auto-connect] 🎯 Window blurred - keyboard input may be disabled');
	});
</script>

<style>
body {
	margin: 0;
	padding: 0;
	background: #000;
	color: #fff;
	font-family: Arial, sans-serif;
}
#main {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 20px;
}
#myCanvas {
	display: block;
	margin: 0;
	padding: 0;
	outline: none; /* 🎯 移除焦点边框 */
}
#status {
	margin: 20px 0;
	font-size: 16px;
}
#debug {
	margin: 10px 0;
	font-size: 12px;
	color: #888;
}
.help {
	margin-top: 20px;
	font-size: 12px;
	color: #ccc;
}
</style>

  </head>

  <body onload='load("myCanvas")'>

    <div id="main" class="container">
    	<div>
    		<h2>UseIt RDP Connection</h2>
    		<div id="status">Initializing...</div>
    		<div id="debug">Loading parameters...</div>
    		<div class="help">
    			Press ESC to disconnect<br>
    			Full screen mode: browser F11
    		</div>
    	</div>
    </div>
    
    <canvas id="myCanvas" style="display:none; outline:none;" tabindex="0">

  </body>
</html> 