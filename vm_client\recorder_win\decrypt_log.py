import json
import base64
from cryptography.fernet import Fernet
import sys
import os
from pathlib import Path

def process_key(encrypted_key):
    """处理加密密钥 - 恢复前半部分的翻转"""
    try:
        # 前17位是被翻转的部分
        first_half = encrypted_key[:17]
        second_half = encrypted_key[17:]
        
        # 重新翻转前半部分
        restored_first_half = first_half[::-1]
        
        # 组合还原后的密钥
        restored_key = restored_first_half + second_half
        
        return restored_key
    except Exception as e:
        print(f"Error processing key: {e}")
        raise

def decrypt_log(input_file_path, output_file_path=None):
    """解密单个日志文件"""
    try:
        with open(input_file_path, 'rb') as f:
            encrypted_key = f.readline().strip()
            key = process_key(encrypted_key)
            fernet = Fernet(key)
            
            if output_file_path is None:
                base_name = os.path.splitext(input_file_path)[0]
                output_file_path = f"{base_name}_decrypted.txt"
            
            with open(output_file_path, 'w', encoding='utf-8') as out_f:
                last_window = None
                for line_number, line in enumerate(f, 1):
                    try:
                        encrypted_data = line.strip()
                        if encrypted_data:
                            decrypted_data = fernet.decrypt(encrypted_data).decode('utf-8')
                            event_data = json.loads(decrypted_data)
                            
                            # 只在窗口变化或特殊消息时显示窗口信息
                            current_window = event_data['window']
                            if (last_window != current_window and 
                                'Active Window' in event_data['message']):
                                formatted_line = (
                                    f"[{event_data['timestamp']}] "
                                    f"{event_data['message']}"
                                )
                                out_f.write(formatted_line + '\n')
                                last_window = current_window
                            else:
                                # 对于其他事件，只显示时间戳和消息
                                if not event_data['message'].startswith('Active Window'):
                                    formatted_line = (
                                        f"[{event_data['timestamp']}] "
                                        f"{event_data['message']}"
                                    )
                                    out_f.write(formatted_line + '\n')
                                    
                    except Exception as e:
                        print(f"Warning: Failed to decrypt line {line_number} in {input_file_path}: {e}")
                        continue
            
            print(f"Successfully decrypted file saved to: {output_file_path}")
            return True
            
    except Exception as e:
        print(f"Error decrypting file {input_file_path}: {e}")
        return False

def process_path(input_path, output_dir=None):
    """处理输入路径（可以是文件或目录）"""
    input_path = Path(input_path)
    
    # 如果指定了输出目录，确保它存在
    if output_dir:
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
    
    if input_path.is_file():
        # 处理单个文件
        if output_dir:
            output_file = output_dir / f"{input_path.stem}_decrypted.txt"
        else:
            output_file = input_path.parent / f"{input_path.stem}_decrypted.txt"
        decrypt_log(str(input_path), str(output_file))
    
    elif input_path.is_dir():
        # 处理目录下的所有.srt文件
        for file_path in input_path.glob("*.srt"):
            if output_dir:
                output_file = output_dir / f"{file_path.stem}_decrypted.txt"
            else:
                output_file = file_path.parent / f"{file_path.stem}_decrypted.txt"
            decrypt_log(str(file_path), str(output_file))
    
    else:
        print(f"Error: Path '{input_path}' does not exist")

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python decrypt_log.py <input_path> [output_directory]")
        print("Examples:")
        print("  python decrypt_log.py input.srt")
        print("  python decrypt_log.py input.srt output_folder")
        print("  python decrypt_log.py input_folder")
        print("  python decrypt_log.py input_folder output_folder")
        return
    
    input_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    process_path(input_path, output_dir)

if __name__ == "__main__":
    main() 