@echo off
echo ================================================
echo Python Service Build Script
echo ================================================

echo.
echo [1/4] Checking Python environment...

REM 检查是否有虚拟环境
if exist "listener\Scripts\activate.bat" (
    echo Found virtual environment: listener
    call listener\Scripts\activate.bat
) else (
    echo No virtual environment found, using system Python
)

echo.
echo [2/4] Installing/updating required packages...

REM 安装必要的包
pip install flask requests werkzeug pyinstaller pynput psutil cryptography waitress win32gui

if errorlevel 1 (
    echo ERROR: Package installation failed
    echo Please check your Python environment and internet connection
    pause
    exit /b 1
)

echo.
echo [3/4] Cleaning old build files...

REM 清理旧的编译文件
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
if exist "InputListener.exe" del InputListener.exe
if exist "_internal" rmdir /s /q _internal

echo.
echo [4/4] Building InputListener.exe...

REM 使用PyInstaller编译
pyinstaller InputListener.spec

if errorlevel 1 (
    echo ERROR: Build failed
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo [5/5] Copying files to correct location...

REM 复制编译好的文件
if exist "dist\InputListener\InputListener.exe" (
    copy "dist\InputListener\InputListener.exe" "InputListener.exe"
    echo InputListener.exe copied to root directory
) else (
    echo ERROR: InputListener.exe not found in dist directory
    pause
    exit /b 1
)

REM 复制_internal目录
if exist "dist\InputListener\_internal" (
    if exist "_internal" rmdir /s /q _internal
    xcopy "dist\InputListener\_internal" "_internal" /E /I /Y
    echo _internal directory copied
) else (
    echo WARNING: _internal directory not found
)

echo.
echo ================================================
echo BUILD SUCCESSFUL!
echo ================================================
echo.
echo Files created:
echo   - InputListener.exe
echo   - _internal\ (directory with dependencies)
echo.
echo You can now test the new APIs:
echo   curl -X POST http://localhost:3000/executeCommand -H "Content-Type: application/json" -d "{\"command\": \"echo Hello World\"}"
echo.
pause
