import sys
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, jsonify, request
import subprocess
import requests
from werkzeug.utils import secure_filename
from pynput import mouse, keyboard
import time
from datetime import datetime
import os
import platform
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.fernet import Fernet
import secrets
import json
import base64
import traceback
import threading
import ctypes
from ctypes import windll, wintypes
from io import BytesIO
import win32gui
import win32process
import psutil
from win32api import GetSystemMetrics
# from uiautomation import uiautomation as auto
# from uiautomation.uiautomation import UIAutomationInitializerInThread
# from PIL import ImageGrab  # 添加PIL库用于截图

app = Flask(__name__)

# 定义保存路径
def get_save_path():
    # 无论是打包后的应用还是开发环境都使用Downloads/record_save目录
    save_path = os.path.join(os.path.expanduser('~'), 'Downloads', 'record_save')
    
    # 确保目录存在
    os.makedirs(save_path, exist_ok=True)
    return save_path

class InputListener:
    def __init__(self):
        # 初始化键盘和鼠标监听器
        self.keyboard_listener = None
        self.mouse_listener = None
        self.is_recording = False
        
        # Windows API 函数（用于UIA）
        self.GetCursorPos = windll.user32.GetCursorPos
        self.GetCursorPos.argtypes = [ctypes.POINTER(wintypes.POINT)]
        self.GetAsyncKeyState = windll.user32.GetAsyncKeyState
        self.VK_LBUTTON = 0x01
        
        # 为每个线程创建一个UIA初始化器字典
        # self.uia_initializers = {}
        
        # 添加鼠标状态跟踪
        self.is_dragging = False
        self.last_click_time = 0
        self.last_click_position = (0, 0)
        self.drag_start_pos = None
        
        # 修改双击检测的配置
        self.double_click_threshold = 0.5  # 双击阈值（秒）
        self.double_click_distance = 5    # 双击距离阈值（像素）
        
        # 添加鼠标移动跟踪相关的属性
        self.last_move_time = 0
        self.move_threshold = 0.1  # 每0.1秒最多输出一次移动信息
        self.last_position = (0, 0)
        self.min_move_distance = 10  # 最小移动距离（像素）
        
        # 定义修饰键
        self.modifier_keys = {
            keyboard.Key.ctrl_l, keyboard.Key.ctrl_r,
            keyboard.Key.shift_l, keyboard.Key.shift_r,
            keyboard.Key.alt_l, keyboard.Key.alt_r,
            keyboard.Key.cmd_l, keyboard.Key.cmd_r
        }

        # 用于跟踪当前按下的修饰键
        self.current_modifiers = set()
        
        # 添加鼠标按钮映射
        self.mouse_button_map = {
            mouse.Button.left: "L",
            mouse.Button.right: "R",
            mouse.Button.middle: "M"
        }

        # 添加事件缓存
        self.event_buffer = []
        
        # 修改保存路径
        self.downloads_path = get_save_path()
        self.start_time = None
        self.log_file = None

        # 添加活跃窗口跟踪
        self.last_active_window = None
        self.window_check_interval = 0.1  # 检查间隔（秒）
        self.last_window_check = 0

        self.encryption_key = None

        self.screen_info = self.get_screen_info()

        self.pending_events = 0  # 添加未处理事件计数器
        
        # 当前录制的截图目录
        # self.current_screenshot_dir = None
        
        # 初始化UIA
        # self.uia_initializers = []
        # 创建UIA初始化器
        # self.uia_initializers.append(UIAutomationInitializerInThread())

    def get_key_name(self, key):
        # 获取按键的可读名称
        try:
            # 添加对小键盘按键的特殊处理
            if hasattr(key, 'vk') and 96 <= key.vk <= 111:  # 小键盘的虚拟键码范围
                # 小键盘映射
                numpad_map = {
                    96: 'NUMPAD_0',
                    97: 'NUMPAD_1',
                    98: 'NUMPAD_2',
                    99: 'NUMPAD_3',
                    100: 'NUMPAD_4',
                    101: 'NUMPAD_5',
                    102: 'NUMPAD_6',
                    103: 'NUMPAD_7',
                    104: 'NUMPAD_8',
                    105: 'NUMPAD_9',
                    106: 'NUMPAD_MULTIPLY',
                    107: 'NUMPAD_ADD',
                    108: 'NUMPAD_ENTER',
                    109: 'NUMPAD_SUBTRACT',
                    110: 'NUMPAD_DECIMAL',
                    111: 'NUMPAD_DIVIDE'
                }
                return numpad_map.get(key.vk, str(key))
            
            if hasattr(key, 'char'):
                # 处理控制字符
                if key.char is None:
                    return key.name.upper() if hasattr(key, 'name') else str(key)
                if ord(key.char) < 32:  # ASCII控制字符
                    # 将控制字符转换为可读格式
                    return chr(ord(key.char) + 64)
                return key.char.upper()
            else:
                return key.name.upper() if hasattr(key, 'name') else str(key)
        except AttributeError:
            return str(key)

    def format_hotkey(self):
        # 格式化当前的组合键
        if not self.current_modifiers:
            return ""
        
        modifiers = []
        for key in self.current_modifiers:
            name = self.get_key_name(key)
            # 简化修饰键名称
            name = name.replace('_L', '').replace('_R', '')
            modifiers.append(name)
        
        return '+'.join(sorted(modifiers))

    def on_key_press(self, key):
        # 在按键时检查活跃窗口
        self.check_active_window()
        
        try:
            if key in self.modifier_keys:
                self.current_modifiers.add(key)
                hotkey = self.format_hotkey()
                if hotkey:
                    # 检测窗口切换快捷键
                    if ('ALT' in hotkey and 
                        (keyboard.Key.tab in self.current_modifiers or 
                         hasattr(key, 'char') and key.char == '\t')):
                        self.log_event("Window Switch: Alt+Tab")
                    elif ('CMD' in hotkey and 
                          (keyboard.Key.tab in self.current_modifiers or 
                           hasattr(key, 'char') and key.char == '\t')):
                        self.log_event("Window Switch: Cmd+Tab")
                    else:
                        self.log_event(f"Hotkey: {hotkey}")
            else:
                key_name = self.get_key_name(key)
                if self.current_modifiers:
                    hotkey = f"{self.format_hotkey()}+{key_name}"
                    # 检测窗口切换快捷键
                    if ('ALT' in hotkey and key_name == 'TAB'):
                        self.log_event("Window Switch: Alt+Tab")
                    elif ('CMD' in hotkey and key_name == 'TAB'):
                        self.log_event("Window Switch: Cmd+Tab")
                    else:
                        self.log_event(f"Hotkey: {hotkey}")
                else:
                    self.log_event(f"Key Press: {key_name}")
        except AttributeError:
            self.log_event(f"Special Key: {key}")

    def on_key_release(self, key):
        if key in self.modifier_keys:
            self.current_modifiers.discard(key)
            
        try:
            key_name = self.get_key_name(key)
            self.log_event(f"Key Release: {key_name}")
        except AttributeError:
            self.log_event(f"Special Key: {key}")

    def get_mouse_button_name(self, button):
        # 获取鼠标按钮的简化名称
        return self.mouse_button_map.get(button, str(button))

    def get_uia_element_info(self, x, y):
        """获取UI自动化元素信息"""
        # 注释掉UIA相关功能
        return None
        
        # try:
        #     # 获取当前线程ID
        #     thread_id = threading.get_ident()
        #     
        #     # 如果当前线程没有初始化器，创建一个
        #     if thread_id not in self.uia_initializers:
        #         self.uia_initializers[thread_id] = UIAutomationInitializerInThread()
        #     
        #     # 使用初始化器
        #     with self.uia_initializers[thread_id]:
        #         element = auto.ControlFromPoint(x, y)
        #         if element:
        #             element_info = {
        #                 "Name": element.Name,
        #                 "ControlType": element.ControlTypeName,
        #                 "AutomationId": element.AutomationId,
        #                 "BoundingRectangle": str(element.BoundingRectangle)
        #             }
        #             
        #             # 获取子元素信息
        #             children = element.GetChildren()
        #             if children:
        #                 child_names = []
        #                 for child in children:
        #                     if child.Name:
        #                         child_names.append(child.Name)
        #                 if child_names:
        #                     element_info["Children"] = child_names
        #             
        #             return element_info
        #     return None
        # except Exception as e:
        #     app.logger.error(f"Error getting UIA element info: {str(e)}")
        #     return None

    def on_mouse_click(self, x, y, button, pressed):
        # 在鼠标点击时检查活跃窗口
        self.check_active_window()
        
        x, y = int(x), int(y)
        button_name = self.get_mouse_button_name(button)

        if pressed:
            current_time_ms = time.time()
            
            # 如果是左键点击且正在录制，则截图
            # if button_name == "L" and self.is_recording:
            #     try:
            #         # 计算相对于录制开始时间的时间戳（精确到毫秒）
            #         current_time = datetime.now()
            #         elapsed_ms = int((current_time - self.start_time).total_seconds() * 1000)
            #         hours = elapsed_ms // 3600000
            #         minutes = (elapsed_ms % 3600000) // 60000
            #         seconds = (elapsed_ms % 60000) // 1000
            #         milliseconds = elapsed_ms % 1000
                    
            #         # 格式化为SRT时间戳格式：HH:MM:SS,毫秒
            #         srt_timestamp = f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
                    
            #         # 截取全屏
            #         screenshot = ImageGrab.grab()
                    
            #         # 将截图转换为base64编码
            #         buffer = BytesIO()
            #         screenshot.save(buffer, format="PNG")
            #         base64_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    
            #         # 增加截图计数器
            #         self.screenshot_srt_count += 1
                    
            #         # 写入SRT格式的截图数据
            #         self.screenshot_srt_file.write(f"{self.screenshot_srt_count}\n")
            #         self.screenshot_srt_file.write(f"{srt_timestamp} --> {srt_timestamp}\n")
            #         self.screenshot_srt_file.write(f"x={x},y={y},base64:{base64_image}\n\n")
            #         self.screenshot_srt_file.flush()  # 确保写入磁盘
                    
            #     except Exception as e:
            #         self.log_event(f"Screenshot error: {str(e)}")
            
            # 检查是否是双击
            if (current_time_ms - self.last_click_time) < self.double_click_threshold:
                dx = abs(x - self.last_click_position[0])
                dy = abs(y - self.last_click_position[1])
                if dx <= self.double_click_distance and dy <= self.double_click_distance:
                    # 获取UIA元素信息
                    # uia_info = self.get_uia_element_info(x, y)
                    click_info = f"{button_name}DoubleClick at ({x}, {y})"
                    # if uia_info:
                    #     click_info += f", uia: {json.dumps(uia_info)}"
                    self.log_event(click_info)
                    self.last_click_time = 0
                    return
            
            # 如果是左键按下，设置拖动起始位置
            if button_name == "L":
                self.drag_start_pos = (x, y)
                self.is_dragging = False
            
            # 记录点击位置和时间
            self.last_click_time = current_time_ms
            self.last_click_position = (x, y)
            
            # 只有在没有拖动状态时才记录点击事件
            if not self.is_dragging:
                # 获取UIA元素信息
                # uia_info = self.get_uia_element_info(x, y)
                click_info = f"{button_name}Click at ({x}, {y})"
                # if uia_info:
                #     click_info += f", uia: {json.dumps(uia_info)}"
                self.log_event(click_info)
        else:
            # 鼠标释放
            if self.is_dragging and self.drag_start_pos:
                drag_end_pos = (x, y)
                total_distance = (
                    x - self.drag_start_pos[0],
                    y - self.drag_start_pos[1]
                )
                # 获取UIA元素信息
                # uia_info = self.get_uia_element_info(x, y)
                drag_info = f"{button_name}DragEnd: from {self.drag_start_pos} to {drag_end_pos}, distance {total_distance}"
                # if uia_info:
                #     drag_info += f", uia: {json.dumps(uia_info)}"
                self.log_event(drag_info)
            else:
                # 获取UIA元素信息
                # uia_info = self.get_uia_element_info(x, y)
                release_info = f"{button_name}Release at ({x}, {y})"
                # if uia_info:
                #     release_info += f", uia: {json.dumps(uia_info)}"
                self.log_event(release_info)
            
            # 无论是否拖动状态，释放按钮时都清除拖动状态和起始位置
            if button_name == "L":
                self.is_dragging = False
                self.drag_start_pos = None

    def on_mouse_move(self, x, y):
        # 在鼠标移动时检查活跃窗口
        self.check_active_window()
        
        x, y = int(x), int(y)
        current_time = time.time()
        
        # 检查是否正在拖动
        if self.drag_start_pos is not None:
            # 如果还没有开始拖动，检查是否应该开始
            if not self.is_dragging:
                dx = abs(x - self.drag_start_pos[0])
                dy = abs(y - self.drag_start_pos[1])
                # 只有当移动距离超过阈值时才开始拖动
                if dx > 5 or dy > 5:
                    self.is_dragging = True
                    self.log_event(f"DragStart at {self.drag_start_pos}")
                    self.last_position = self.drag_start_pos
                    self.last_move_time = current_time
            else:
                # 已经在拖动状态，记录移动
                if (current_time - self.last_move_time) >= self.move_threshold:
                    distance = (x - self.last_position[0], y - self.last_position[1])
                    if abs(distance[0]) > 5 or abs(distance[1]) > 5:
                        self.log_event(f"DragMove to ({x}, {y})")
                        self.last_move_time = current_time
                        self.last_position = (x, y)

    def on_mouse_scroll(self, x, y, dx, dy):
        x, y = int(x), int(y)
        scroll_direction = "ScrollUp" if dy > 0 else "ScrollDown"
        self.log_event(f"{scroll_direction} at ({x}, {y})")

    def log_event(self, message):
        if self.is_recording and self.encryption_key:
            try:
                self.pending_events += 1  # 增加待处理事件计数
                current_time = datetime.now()
                elapsed_ms = int((current_time - self.start_time).total_seconds() * 1000)
                hours = elapsed_ms // 3600000
                minutes = (elapsed_ms % 3600000) // 60000
                seconds = (elapsed_ms % 60000) // 1000
                milliseconds = elapsed_ms % 1000
                relative_timestamp = f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"
                
                # 清理消息中的特殊字符
                message = message.encode('ascii', 'ignore').decode('ascii')
                
                event_data = {
                    'timestamp': relative_timestamp,
                    'message': message,
                    'window': self.last_active_window
                }
                
                # 加密数据
                encrypted_data = self.encryption_key.encrypt(
                    json.dumps(event_data).encode()
                ).decode()
                self.event_buffer.append(encrypted_data)
                
                # 使用 sys.stdout 直接写入，避免编码问题
                sys.stdout.buffer.write(f"[{relative_timestamp}] {message}\n".encode('utf-8'))
                sys.stdout.buffer.flush()
                
            except Exception as e:
                app.logger.error(f"Error in log_event: {str(e)}")
            finally:
                self.pending_events -= 1  # 事件处理完成，减少计数

    def _generate_encryption_key(self):
        """生成32字节长度的随机密钥，前半部分倒序"""
        # 生成随机字节
        random_bytes = secrets.token_bytes(16)  # 生成16字节的随机数据
        # 复制一份得到32字节
        full_bytes = random_bytes + random_bytes
        # 转换为base64编码
        key = base64.urlsafe_b64encode(full_bytes)  # 长度为44字节
        
        first_half = key[:17]
        second_half = key[17:]
        
        # 前半部分倒序
        reversed_first_half = first_half[::-1]
        
        # 组合新的密钥
        processed_key = reversed_first_half + second_half
        
        self.encryption_key = Fernet(key)  # 注意：使用原始key创建Fernet对象
        return processed_key  # 返回处理后的密钥

    def get_screen_info(self):
        """获取屏幕信息"""
        try:
            import win32api
            import ctypes
            from ctypes import windll
            
            # 设置DPI感知
            PROCESS_PER_MONITOR_DPI_AWARE = 2
            windll.shcore.SetProcessDpiAwareness(PROCESS_PER_MONITOR_DPI_AWARE)
            
            monitors = win32api.EnumDisplayMonitors()
            scaling_factors = []
            
            def enum_display_monitors(handle, _dc, rect, _data):
                monitor = windll.user32.MonitorFromRect(rect, 0)
                dpi_x = ctypes.c_uint()
                dpi_y = ctypes.c_uint() 
                windll.shcore.GetDpiForMonitor(
                    monitor,
                    0,  # MDT_EFFECTIVE_DPI
                    ctypes.byref(dpi_x),
                    ctypes.byref(dpi_y)
                )
                scaling = dpi_x.value / 96.0
                scaling_factors.append(scaling)
                return True
                
            callback = ctypes.WINFUNCTYPE(
                ctypes.c_bool,
                ctypes.c_size_t,
                ctypes.c_size_t,
                ctypes.POINTER(ctypes.c_long),
                ctypes.c_size_t
            )(enum_display_monitors)
            
            windll.user32.EnumDisplayMonitors(None, None, callback, 0)
            
            # 创建新的屏幕信息字典
            screen_info = {}
            
            for i, monitor in enumerate(monitors):
                left, top, right, bottom = monitor[2]
                scaling = scaling_factors[i] if i < len(scaling_factors) else 1.0
                
                # 计算宽度和高度
                width = right - left
                height = bottom - top
                
                # 检查是否为主显示器（坐标为0,0的显示器）
                is_primary = (left == 0 and top == 0)
                
                # 将显示器信息添加到字典中
                monitor_info = {
                    'x0': left,
                    'y0': top,
                    'width': width,
                    'height': height,
                    'scale_factor': round(scaling, 2)
                }
                
                # 主显示器放在'0'键下，其他显示器按顺序编号
                if is_primary:
                    screen_info['0'] = monitor_info
                else:
                    # 找到第一个未使用的非零索引
                    index = 1
                    while str(index) in screen_info:
                        index += 1
                    screen_info[str(index)] = monitor_info
                
        except Exception as e:
            print(f"Error getting screen info: {e}")
            # 提供默认值
            screen_info = {
                '0': {
                    'x0': 0,
                    'y0': 0,
                    'width': 1920,
                    'height': 1080,
                    'scale_factor': 1.0
                }
            }

        if not screen_info:
            screen_info = {
                '0': {
                    'x0': 0,
                    'y0': 0,
                    'width': 1920,
                    'height': 1080,
                    'scale_factor': 1.0
                }
            }

        return screen_info

    def start_recording(self):
        if not self.is_recording:
            self.event_buffer = []
            self.is_recording = True
            self.start_time = datetime.now()
            
            # 创建新的日志文件
            timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
            video_timestamp = self.start_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log_path = os.path.join(self.downloads_path, f"input_log_{timestamp}.srt")
            
            # # 创建截图SRT文件路径
            # screenshot_srt_path = os.path.join(self.downloads_path, f"sc_{timestamp}.srt")
            # self.screenshot_srt_file = open(screenshot_srt_path, 'w')  # 以文本模式打开截图SRT文件
            # self.screenshot_srt_count = 0  # 初始化截图序号计数器
            
            # 设置当前录制时间戳（不再创建目录）
            # self.current_screenshot_dir = timestamp
            
            self.log_file = open(log_path, 'wb')  # 以二进制模式打开加密文件
            
            # 生成新的加密密钥
            key = self._generate_encryption_key()
            
            # 获取屏幕信息
            screen_info = self.get_screen_info()
            
            # 写入屏幕信息作为第一条记录
            screen_info_data = {
                'timestamp': '00:00:00.000',
                'message': json.dumps(screen_info),
                'window': 'System Info'
            }
            
            # 加密版本
            encrypted_screen_info = self.encryption_key.encrypt(
                json.dumps(screen_info_data).encode()
            ).decode()
            
            # 写入密钥和加密的屏幕信息
            self.log_file.write(key + b"\n")
            self.log_file.write(encrypted_screen_info.encode() + b"\n")
            
            # 写入元数据
            metadata = {
                'video_start_time': video_timestamp,
                'start_message': 'Mouse and keyboard monitoring service started',
                'recording_timestamp': timestamp
            }
            
            # 加密元数据
            encrypted_metadata = self.encryption_key.encrypt(json.dumps(metadata).encode())
            self.log_file.write(encrypted_metadata + b"\n")
            
            # 启动监听器
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.mouse_listener = mouse.Listener(
                on_move=self.on_mouse_move,
                on_click=self.on_mouse_click,
                on_scroll=self.on_mouse_scroll
            )
            
            self.keyboard_listener.start()
            self.mouse_listener.start()
            
            # 记录初始活跃窗口
            initial_window = self.get_active_window_info()
            self.last_active_window = initial_window
            self.log_event(f"Initial Active Window: {initial_window}")
            return {
                'log_path': log_path
            }
        return None

    def stop_recording(self):
        if self.is_recording:
            self.is_recording = False
            filepath = None
            # screenshot_srt_path = None
            # recording_timestamp = self.current_screenshot_dir
            
            def finalize():
                # 停止监听器
                if self.keyboard_listener:
                    self.keyboard_listener.stop()
                    self.keyboard_listener.join()
                if self.mouse_listener:
                    self.mouse_listener.stop()
                    self.mouse_listener.join()
                
                # 等待所有事件处理完成
                while self.pending_events > 0:
                    time.sleep(0.1)
                
                # 清理并写入事件
                self.cleanup_and_write_events()
                
                # 关闭日志文件
                if self.log_file:
                    self.log_file.close()
                    self.log_file = None
                
                # 清理所有UIA初始化器
                # self.uia_initializers.clear()
            
            # 获取文件路径（在启动后台线程前）
            if self.log_file:
                filepath = self.log_file.name
            
            # 关闭截图SRT文件并获取路径
            # if hasattr(self, 'screenshot_srt_file') and self.screenshot_srt_file:
            #     screenshot_srt_path = self.screenshot_srt_file.name
            #     self.screenshot_srt_file.close()
            #     self.screenshot_srt_file = None
            
            # 在后台线程中处理清理工作
            threading.Thread(target=finalize, daemon=True).start()
            
            # 重置截图目录
            # self.current_screenshot_dir = None
            
            return {
                'log_path': filepath,
                # 'screenshot_dir': recording_timestamp,
                # 'screenshot_srt': screenshot_srt_path
            }
        return None

    def cleanup_and_write_events(self):
        if not self.event_buffer or not self.log_file:
            return
        
        # 写入加密数据
        for encrypted_data in self.event_buffer:
            self.log_file.write(encrypted_data.encode() + b'\n')
            
        self.log_file.flush()
        
        # 清空缓冲区
        self.event_buffer = []

    def get_active_window_info(self):
        """获取当前活跃窗口信息"""
        try:
            window = win32gui.GetForegroundWindow()
            title = win32gui.GetWindowText(window)
            _, pid = win32process.GetWindowThreadProcessId(window)
            try:
                process = psutil.Process(pid)
                process_name = process.name()
                # 清理窗口标题中的特殊字符
                title = title.encode('ascii', 'ignore').decode('ascii')
                process_name = process_name.encode('ascii', 'ignore').decode('ascii')
                return f"{process_name} - {title}"
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                return "Unknown"
        except Exception as e:
            app.logger.error(f"Error getting window info: {str(e)}")
            return "Error getting window info"

    def check_active_window(self):
        """检查并记录活跃窗口变化"""
        try:
            current_time = time.time()
            
            # 检查是否达到检测间隔
            if (current_time - self.last_window_check) >= self.window_check_interval:
                current_window = self.get_active_window_info()
                
                # 如果窗口发生变化，记录日志
                if current_window != self.last_active_window:
                    self.log_event(f"Active Window: {current_window}")
                    self.last_active_window = current_window
                
                self.last_window_check = current_time
        except Exception as e:
            app.logger.error(f"Error in check_active_window: {str(e)}")

# 创建全局监听器实例
input_listener = InputListener()

@app.route('/startRecording', methods=['POST'])
def start_recording():
    filepath = input_listener.start_recording()
    if filepath:
        return jsonify({
            'status': 'success',
            'message': 'Input recording started successfully'
        })
    return jsonify({
        'status': 'error',
        'message': 'Recording is already in progress'
    })

@app.route('/stopRecording', methods=['POST'])
def stop_recording():
    result = input_listener.stop_recording()
    if result:
        return jsonify({
            'status': 'success',
            'message': 'Input recording stopped successfully',
            'filename': result['log_path'],
            # 'screenshot_dir': result['screenshot_dir'],
            # 'screenshot_srt': result['screenshot_srt']
        })
    return jsonify({
        'status': 'error',
        'message': 'No recording in progress'
    })

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'ok'})

@app.route('/uploadFile', methods=['POST'])
def upload_file():
    """
    文件上传接口
    接收文件数据和目标路径，将文件保存到指定位置

    请求格式:
    {
        "file_data": "base64编码的文件内容",
        "file_path": "目标文件路径",
        "filename": "文件名"
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No JSON data provided'
            }), 400

        # 检查必需的字段
        required_fields = ['file_data', 'file_path', 'filename']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'Missing required field: {field}'
                }), 400

        file_data = data['file_data']
        target_path = data['file_path']
        filename = secure_filename(data['filename'])

        # 解码base64文件数据
        try:
            file_content = base64.b64decode(file_data)
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Invalid base64 data: {str(e)}'
            }), 400

        # 确保目标目录存在
        target_dir = os.path.dirname(target_path)
        if target_dir and not os.path.exists(target_dir):
            try:
                os.makedirs(target_dir, exist_ok=True)
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to create directory: {str(e)}'
                }), 500

        # 如果target_path是目录，则在其中创建文件
        if os.path.isdir(target_path):
            full_path = os.path.join(target_path, filename)
        else:
            full_path = target_path

        # 写入文件
        try:
            with open(full_path, 'wb') as f:
                f.write(file_content)

            return jsonify({
                'status': 'success',
                'message': 'File uploaded successfully',
                'file_path': full_path,
                'file_size': len(file_content)
            })

        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Failed to write file: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Upload failed: {str(e)}'
        }), 500

@app.route('/executeCommand', methods=['POST'])
def execute_command():
    """
    命令执行接口
    执行本机发送的cmd脚本

    请求格式:
    {
        "command": "要执行的命令",
        "working_dir": "工作目录(可选)",
        "timeout": 超时时间秒数(可选，默认30秒)
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No JSON data provided'
            }), 400

        # 检查必需的字段
        if 'command' not in data:
            return jsonify({
                'status': 'error',
                'message': 'Missing required field: command'
            }), 400

        command = data['command']
        working_dir = data.get('working_dir', None)
        timeout = data.get('timeout', 30)

        # 验证工作目录
        if working_dir and not os.path.exists(working_dir):
            return jsonify({
                'status': 'error',
                'message': f'Working directory does not exist: {working_dir}'
            }), 400

        try:
            # 执行命令
            result = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            return jsonify({
                'status': 'success',
                'message': 'Command executed successfully',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': command,
                'working_dir': working_dir or os.getcwd()
            })

        except subprocess.TimeoutExpired:
            return jsonify({
                'status': 'error',
                'message': f'Command timed out after {timeout} seconds',
                'command': command
            }), 408

        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Command execution failed: {str(e)}',
                'command': command
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Request processing failed: {str(e)}'
        }), 500

# 添加CORS支持
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
    response.headers.add('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS')
    return response

if __name__ == '__main__':
    # For development only - use server.py for production
    from waitress import serve
    app.logger.handlers = logging.getLogger().handlers
    serve(app, host='127.0.0.1', port=4000, threads=4)