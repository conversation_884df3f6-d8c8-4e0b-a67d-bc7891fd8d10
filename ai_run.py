# AI Run console controller
import json
import logging
import sys
import time
from typing import Any, Dict, Generator, Optional
import uuid
from datetime import datetime
import requests
import os

from flask import Response, request, stream_with_context
from flask_login import current_user
from flask_restful import Resource, reqparse
from werkzeug.exceptions import BadRequest, InternalServerError, NotFound

from controllers.console.wraps import account_initialization_required, setup_required
from core.app.entities.app_invoke_entities import InvokeFrom
from core.app.apps.base_app_queue_manager import AppQueueManager
from extensions.ext_database import db
from libs import helper
from libs.login import login_required
from models import App, Account
from services.app_generate_service import AppGenerateService

# pc services
from services.cloud_pc_service import cloud_pc_service
from services.local_pc_service import local_pc_service
from services.local_vm_service import local_vm_service


logger = logging.getLogger(__name__)

# Get AI Run Server URL from environment
AI_RUN_SERVER_URL = os.getenv("AI_RUN_SERVER_URL", "http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com")
logger.info(f"AI Run Server URL configured: {AI_RUN_SERVER_URL}")

# In-memory storage for AI run instances (in production, use Redis or database)
ai_run_instances: Dict[str, Dict[str, Any]] = {}

def call_external_ai_run_server(json_data: Dict = None) -> Optional[Dict]:
    """Call the external AI Run Server's generate_action endpoint"""
    url = f"{AI_RUN_SERVER_URL}/generate_action"
    logger.info(f"Calling external AI Run Server: POST {url}")
    logger.debug(f"Payload: {json.dumps(json_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=json_data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5 minute timeout
        )
        
        logger.info(f"External AI Run Server response: {response.status_code} - Content length: {len(response.text) if response.text else 0}")
        
        if response.status_code in [200, 201]:
            return response.json()
        else:
            logger.warning(f"External AI Run Server returned {response.status_code}: {response.text[:500]}")
            return None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to call external AI Run Server at {url}: {e}")
        return None

class ConsoleAiRunStartApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self):
        """
        Create an AI run instance and return the run instance ID.
        AI processing will start when frontend calls dispatch_input after RDP connection is ready.
        """
        parser = reqparse.RequestParser()
        parser.add_argument("workflowId", type=str, required=True, location="json")
        parser.add_argument("message", type=str, required=True, location="json")
        parser.add_argument("target_pc_url", type=str, required=True, location="json")
        parser.add_argument("metadata", type=dict, required=False, location="json")
        args = parser.parse_args()

        try:
            account = current_user
            app_model = db.session.query(App).filter(App.id == args["workflowId"]).first()
            if not app_model:
                raise NotFound("Workflow not found")
            
            run_instance_id = str(uuid.uuid4())
            
            # Extract AI run mode from metadata (default to 'cloud')
            metadata = args.get("metadata", {})
            ai_run_mode = metadata.get("aiRunMode", "cloud")  # "cloud", "local", "local_pc"
            
            ai_run_instance = {
                "id": run_instance_id,
                "workflowId": app_model.id,
                "status": "initialized",  # Start as initialized, not running
                "createdAt": int(time.time() * 1000),
                "updatedAt": int(time.time() * 1000),
                "metadata": metadata,
                "aiRunMode": ai_run_mode,  # Store the mode
                "messages": [],
                "action_history": {},
                "is_processing": False,  # Not processing yet
                "is_paused": False,
                "user_id": account.id,
                "cloud_pc_user_id": str(account.id),
                "target_pc_url": args["target_pc_url"],
                "initial_message": args["message"],  # Store initial message for later processing
            }
            
            ai_run_instances[run_instance_id] = ai_run_instance
            
            # Return success with run instance ID - no AI processing started yet
            mode_message = (
                "AI run instance initialized. Establishing Local VM connection..." if ai_run_mode == "local" 
                else "AI run instance initialized. Establishing RDP connection..."
            )
            
            return {
                "status": "success",
                "message": f"AI run instance created. Ready for {ai_run_mode} mode.",
                "runInstanceId": run_instance_id,
                "aiRunMode": ai_run_mode,
                "ai_run_response": {
                    "type": "text",
                    "text": mode_message
                }
            }, 200

        except Exception as e:
            logger.error(f"Error creating AI run instance: {e}")
            raise InternalServerError("Failed to create AI run instance")

    def _process_ai_run_response(self, response_data: Dict, ai_run_instance: Dict) -> Dict:
        """Process AI Run Server response and execute cloud PC actions if needed"""

        # Example response data:
        # 1. flow control
        # simulated_response_flow_control = {
        #     'action_history': ['Planner: , Actor: Processed start node: 开始'], 
        #     'complete_flag': False, 
        #     'current_milestone_id': '1205703235401', 
        #     'generated_action': {'message': 'Completed start node', 'type': 'continue'}, 
        #     'generated_plan': {
        #         'action_from_planner': 'Proceed to the first actual task.',
        #         'milestone_goal': 'Processed start node 1205703235401.',
        #         'observation': 'Processed start node 1205703235401.',
        #         'reasoning': 'This is the starting point of the flow.',
        #         'step_info': 'This is the starting point of the flow.'
        #     }, 
        #     'milestone_complete_flag': True, 
        #     'milestone_md': "### Task Progress:\n## 开始 - Completed\n## GUI 里程碑 - Current  Objective\n   - Goal: Open the chatbox with 'Boss'\n   - No actions taken yet for this objective.", 
        #     'node_type': 'start', 
        #     'status': 'success'
        # }
        # infer_server_response = simulated_response_flow_control

        # 2. gui milestone
        # simulated_response_gui = {
        #     'action_history': ['Planner: Check if the screen is on or if the application is running, and ensure the WeChat interface is visible., Actor: Moved mouse at [949, 527].'],
        #     'complete_flag': False,
        #     'current_milestone_id': '1776681713054',
        #     'generated_action': {'action': 'MOVE', 'position': [949, 527], 'value': ''},
        #     'generated_plan': {
        #         'action_from_planner': 'Check if the screen is on or if the application is running, and ensure the WeChat interface is visible.', 'milestone_goal': "Open the chatbox with 'Boss'", 
        #         'observation': 'The screenshot is completely black, indicating that either the screen is off, the application is not visible, or there is an issue with the screenshot capture.', 
        #         'reasoning': "Since the screen is not visible, I cannot identify the WeChat interface or locate the 'Boss' contact to proceed with opening the chatbox. According to the Guidance Trajectory, the first step is to select the 'Boss' contact from the WeChat contact list, but I need to see the interface to do so.", 
        #         'step_info': 'Select the Boss contact from the WeChat contact list.'
        #     },
        #     'milestone_complete_flag': False,
        #     'milestone_md': "### Task Progress:\n## 开始 - Completed\n## GUI 里程碑 - Current Objective\n   - Goal: Open the chatbox with 'Boss'\n   - Actions Taken So Far:\n     - Step 1: Planner: Check if the screen is on or if the application is running, and ensure the WeChat interface is visible., Actor: Moved mouse at [949, 527].",
        #     'node_type': 'gui-milestone',
        #     'status': 'success'
        # }
        # infer_server_response = simulated_response_gui

        # run_instance_id = ai_run_instance.get("id")
        # logger.info(f"Processing AI Run response for instance {run_instance_id}: {str(response_data)[:1000]}")
        
        result = {"response_data": response_data, "completed": False}
        
        if response_data.get('status') == 'success':
            
            # Prepare frontend-visible message
            response_content = self._prepare_frontend_visable_message(response_data)

            # Create the assistant message with the formatted content
            if response_content:
                text_message = {
                    "id": str(uuid.uuid4()), 
                    "role": "assistant", 
                    "type": "text", 
                    "content": response_content.strip(), 
                    "timestamp": int(time.time() * 1000)
                }
                ai_run_instance["messages"].append(text_message)
            
            # Create a formatted response for the frontend that matches what it expects
            formatted_response = {
                "type": "text",
                "text": response_content.strip() if response_content else "Processing...",
                "completed": response_data.get('complete_flag', False),
                "milestone_completed": response_data.get('milestone_complete_flag', False),
                "status": "success"
            }
            
            # Update the result to include the formatted response
            result["response_data"] = formatted_response
            
        else:
            formatted_error_response = {
                "type": "text",
                "text": response_data.get('error', 'Unknown error'),
                "completed": False,
                "milestone_completed": False,
                "status": "error"
            }   
            result["response_data"] = formatted_error_response

        # # Execute actions based on AI run mode
        # if "cloud_pc_actions" in response_data or "generated_action" in response_data:
        #     ai_run_mode = ai_run_instance.get("aiRunMode", "cloud")
        #     if ai_run_mode == "local":
        #         result["vm_execution"] = self._execute_local_vm_actions(response_data, ai_run_instance)
        #     elif ai_run_mode == "local_pc":
        #         result["vm_execution"] = self._execute_local_pc_actions(response_data, ai_run_instance)
        #     else:
        #         result["cloud_pc_execution"] = self._execute_cloud_pc_actions(response_data, ai_run_instance)
        
        # result["completed"] = response_data.get('completed', False)
        # ai_run_instance["updatedAt"] = int(time.time() * 1000)
        
        return result
    
    def _prepare_frontend_visable_message(self, response_data):
        """
        Process the AI response data to create a frontend-visible message.
        """
        
        # Extract and format the AI planning information
        generated_plan = response_data.get('generated_plan', {})
        generated_action = response_data.get('generated_action', {})
        milestone_md = response_data.get('milestone_md', '')
            
        # Create a comprehensive response message
        response_content = ""
        
        # Add milestone progress if available
        if milestone_md:
            response_content += f"**Task Progress:**\n{milestone_md}\n\n"
        
        # Add planner reasoning
        if generated_plan.get('reasoning'):
            response_content += f"**AI Analysis:** {generated_plan['reasoning']}\n\n"
        
        # Add planned action
        if generated_plan.get('action_from_planner'):
            response_content += f"**Planned Action:** {generated_plan['action_from_planner']}\n\n"
        
        # Add action details
        if generated_action:
            action_type = generated_action.get('action', 'Unknown')
            if action_type == 'CLICK':
                position = generated_action.get('position', [0, 0])
                response_content += f"**Executing:** Click at position ({position[0]}, {position[1]})\n\n"
            elif action_type == 'TYPE':
                value = generated_action.get('value', '')
                response_content += f"**Executing:** Type '{value}'\n\n"
            else:
                response_content += f"**Executing:** {action_type}\n\n"
        
        # Add observation if available
        if generated_plan.get('observation'):
            response_content += f"**Current State:** {generated_plan['observation']}\n\n"
        
        # Add step info if available
        if generated_plan.get('step_info'):
            response_content += f"**Next Step:** {generated_plan['step_info']}\n\n"
    
        # Add completion status
        if response_data.get('complete_flag'):
            response_content += "**Status:** Task completed! ✅"
        elif response_data.get('milestone_complete_flag'):
            response_content += "**Status:** Milestone completed! Moving to next objective."
        else:
            response_content += "**Status:** In progress..."
        
        return response_content
    
    def _execute_cloud_pc_actions(self, ai_run_response: Dict, ai_run_instance: Dict) -> Dict:
        """Extracts the action and sends it to the Cloud PC's execute_action endpoint."""
        cloud_pc_user_id = ai_run_instance.get("cloud_pc_user_id")
        run_instance_id = ai_run_instance.get("id")
        
        # Extract the action from the AI Run Server response
        action_to_execute = ai_run_response.get("generated_action")
        
        if not action_to_execute:
            logger.warning(f"No 'generated_action' found in AI Run Server response for instance {run_instance_id}.")
            return {"success": False, "error": "No action found in AI response to execute."}

        # Prepare the payload for the Cloud PC's /execute_action endpoint.
        # It expects a top-level "action" key, and the executor within it
        # expects a "content" key inside that.
        pc_payload = { "action": { "content": action_to_execute } }
        
        try:
            logger.info(f"Sending action to Cloud PC for instance {run_instance_id}: {json.dumps(pc_payload, indent=2)}")
            # Call the cloud pc service with the correctly structured payload
            result = cloud_pc_service.execute_action(cloud_pc_user_id, pc_payload)
            logger.info(f"Cloud PC action result for instance {run_instance_id}: {result}")
            
            # Log the action execution
            action_log = {
                "id": str(uuid.uuid4()), 
                "role": "system", 
                "type": "cloud_pc_action", 
                "content": "Sent action to Cloud PC execute_action endpoint.", 
                "timestamp": int(time.time() * 1000), 
                "sent_action": pc_payload,
                "cloud_pc_result": result
            }
            ai_run_instance["messages"].append(action_log)
            
            return result
            
        except Exception as e:
            logger.error(f"Error during Cloud PC action execution for instance {run_instance_id}: {e}", exc_info=True)
            error_result = {"success": False, "error": str(e)}
            
            # Log the error
            error_log = {
                "id": str(uuid.uuid4()), 
                "role": "system", 
                "type": "cloud_pc_action", 
                "content": f"Failed to execute Cloud PC action: {str(e)}", 
                "timestamp": int(time.time() * 1000), 
                "sent_action": pc_payload,
                "error": str(e)
            }
            ai_run_instance["messages"].append(error_log)
            
            return error_result
    
    def _execute_local_vm_actions(self, ai_run_response: Dict, ai_run_instance: Dict) -> Dict:
        """Execute actions on Local VM through electron app integration."""
        
        return "_execute_local_vm_actions success"
        
        # run_instance_id = ai_run_instance.get("id")

        # try:
        #     logger.info(f"Executing action on Local VM for instance {run_instance_id}: {str(ai_run_response)[:1000]}")
            
        #     # For Local VM mode, we'll return a success response with action details
        #     # The actual VM interaction will be handled by the electron app
        #     # This could be extended to make actual calls to electron APIs if needed

        #     action_to_execute = ai_run_response.get("generated_action")

        #     if not action_to_execute:
        #         logger.warning(f"No 'generated_action' found in AI Run Server response for Local VM instance {run_instance_id}.")
        #         return {"success": False, "error": "No action found in AI response to execute on Local VM."}

        #     # direct repost to local vm ootb
        #     action_payload = ai_run_response

        #     # TODO: Execute action on Local VM
        #     # from xxx import local_vm_service
        #     # result = {
        #     #     "success": True,
        #     #     "action": "local_vm_action",
        #     #     "mode": "local_vm",
        #     #     "message": "Action queued for Local VM execution via electron app"
        #     # }

        #     # result = local_vm_service.execute_action(local_vm_id, action_payload)

            
        #     # Log the action execution
        #     action_log = {
        #         "id": str(uuid.uuid4()), 
        #         "role": "system", 
        #         "type": "local_vm_action", 
        #         "content": "Action sent to Local VM via electron app.", 
        #         "timestamp": int(time.time() * 1000), 
        #         "sent_action": {"action": action_to_execute},
        #         "vm_result": result
        #     }
        #     ai_run_instance["messages"].append(action_log)
            
        #     logger.info(f"Local VM action result for instance {run_instance_id}: {str(result)[:1000]}")
        #     return result
            
        # except Exception as e:
        #     logger.error(f"Error during Local VM action execution for instance {run_instance_id}: {e}", exc_info=True)
        #     error_result = {"success": False, "error": str(e), "mode": "local_vm"}
            
        #     # Log the error
        #     error_log = {
        #         "id": str(uuid.uuid4()), 
        #         "role": "system", 
        #         "type": "local_vm_action", 
        #         "content": f"Failed to execute Local VM action: {str(e)}", 
        #         "timestamp": int(time.time() * 1000), 
        #         "sent_action": {"action": action_to_execute},
        #         "error": str(e)
        #     }
        #     ai_run_instance["messages"].append(error_log)
            
        #     return error_result

    def _execute_local_pc_actions(self, ai_run_response: Dict, ai_run_instance: Dict) -> Dict:
        """Execute actions on Local VM through electron app integration."""
        run_instance_id = ai_run_instance.get("id")
        
        try:
            logger.info(f"Executing action on Local PC for instance {run_instance_id}: {ai_run_response}")
            
            action_to_execute = ai_run_response.get("generated_action")

            if not action_to_execute:
                    logger.warning(f"No 'generated_action' found in AI Run Server response for Local VM instance {run_instance_id}.")
                    return {"success": False, "error": "No action found in AI response to execute on Local VM."}

            # direct repost to local pc ootb
            action_payload = ai_run_response

            local_pc_url = "http://localhost:7899"

            # TODO: Execute action on Local PC
            result = local_pc_service.execute_action(action_payload, local_pc_url)
            
            result = {
                "success": True,
                "action": action_payload,
                "mode": "local_vm",
                "message": "Action queued for Local PC execution via electron app"
            }
            
            # Log the action execution
            action_log = {
                "id": str(uuid.uuid4()), 
                "role": "system", 
                "type": "local_vm_action", 
                "content": "Action sent to Local PC via electron app.", 
                "timestamp": int(time.time() * 1000), 
                "sent_action": {"action": action_to_execute},
                "vm_result": result
            }
            ai_run_instance["messages"].append(action_log)
            
            logger.info(f"Local VM action result for instance {run_instance_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error during Local PC action execution for instance {run_instance_id}: {e}", exc_info=True)
            error_result = {"success": False, "error": str(e), "mode": "local_vm"}
            
            # Log the error
            error_log = {
                "id": str(uuid.uuid4()), 
                "role": "system", 
                "type": "local_vm_action", 
                "content": f"Failed to execute Local PC action: {str(e)}", 
                "timestamp": int(time.time() * 1000), 
                "sent_action": {"action": action_to_execute},
                "error": str(e)
            }
            ai_run_instance["messages"].append(error_log)
            
            return error_result
        

class ConsoleAiRunMessagesApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, run_instance_id: str):
        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        parser = reqparse.RequestParser()
        parser.add_argument("limit", type=int, location="args", default=50)
        args = parser.parse_args()
        
        messages = instance.get("messages", [])
        if args["limit"]: messages = messages[-args["limit"]:]
        
        return {"messages": messages, "total": len(instance.get("messages", []))}

class ConsoleAiRunDispatchInputApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, run_instance_id: str):
        parser = reqparse.RequestParser()
        parser.add_argument("content", type=str, required=True, location="json")
        parser.add_argument("metadata", type=dict, required=False, location="json")
        args = parser.parse_args()

        with open("ai_run_console_api_input.txt", "a") as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')}\n{json.dumps({
                "run_instance_id": run_instance_id,
                "content": args['content'],
                "metadata": args['metadata']
            }, indent=2)}\n")
        
        # Get the instance and validate
        instance = ai_run_instances.get(run_instance_id)
        if not instance: 
            raise NotFound("AI run instance not found")
        if instance.get("user_id") != current_user.id: 
            raise NotFound("AI run instance not found")
        
        # For subsequent calls, only add user message if it's not a "continue" command from auto-continuation
        if args['content'].strip().lower() != 'continue':
            user_message = {
                "id": str(uuid.uuid4()), 
                "role": "user", 
                "type": "text", 
                "content": args['content'], 
                "timestamp": int(time.time() * 1000)
            }
            instance["messages"].append(user_message)

        # Update metadata if provided
        if args.get('metadata'):
            instance["metadata"].update(args.get('metadata'))
            # Update AI run mode if changed
            if "aiRunMode" in args.get('metadata', {}):
                instance["aiRunMode"] = args.get('metadata')["aiRunMode"]

        # in msg 0 - dispatch
        # out msg 0 - get screenshot (callback)

        # in msg 1 - screenshot (callback)
        # screenshot + msg 0 -> ai run server -> ai run response
        # out msg 1 - ai run response
        
        # callback to get screenshot
        if not args.get('metadata'):
            logger.info("in msg type 0 - _dispatch_get_screenshot (call back to frontend)")
            return self._dispatch_get_screenshot(run_instance_id, args['content'], args.get('metadata'))
        
        # got screenshot, continue to dispatch
        elif args.get('metadata').get('action_method') == "get_screenshot" and \
             args.get('metadata').get('source') == "vm_execution_result" and \
             args.get('metadata').get('vm_response').get('screenshot'):
            logger.info("in msg type 1 - _dispatch (call ai run server)")
            return self._dispatch(run_instance_id, args['content'], args.get('metadata'))

        else:
            logger.info("in msg type 2 - ERROR")
            raise InternalServerError("Invalid metadata (not null metadata or get_screenshot)")
    
    def _dispatch_get_screenshot(self, run_instance_id: str, query: str, metadata: dict):
        """callback frontend to get screenshot """
        response = {
            "status": "success",
            "generated_action": {
                "method": "get_screenshot",
                "data":{
                    "screen_index": 0,
                    "resize": False,
                }
            }
        }
        with open("ai_run_console_api_output.txt", "a") as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} _dispatch_get_screenshot\n{json.dumps(response, indent=2)}\n")
        return response, 200
    
    def _dispatch(self, run_instance_id: str, query: str, metadata: dict):

        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        instance["is_processing"] = True
        
        # Get AI run mode
        ai_run_mode = instance.get("aiRunMode", "cloud")
        
        # CAPTURE SCREENSHOT based on mode
        if ai_run_mode == "cloud":
            # Cloud Computer mode: get screenshot from Cloud PC
            cloud_pc_user_id = instance.get("cloud_pc_user_id")
            screenshot_result = cloud_pc_service.get_screenshot(cloud_pc_user_id)
            
            if not screenshot_result.get("success"):
                raise InternalServerError(f"Failed to get Cloud PC screenshot: {screenshot_result.get('error')}")
            
            screenshot = screenshot_result.get("screenshot")
            if not screenshot:
                raise InternalServerError("No screenshot available from Cloud PC")
            
            # ADD SCREENSHOT THUMBNAIL TO CHAT
            screenshot_message = {
                "id": str(uuid.uuid4()),
                "role": "assistant",
                "type": "image",
                "content": "Screenshot captured from Cloud PC",
                "timestamp": int(time.time() * 1000),
                "imageUrl": f"data:image/png;base64,{screenshot}" if not screenshot.startswith('data:') else screenshot
            }
            instance["messages"].append(screenshot_message)
            logger.info(f"Added Cloud PC screenshot thumbnail to chat for instance {run_instance_id}")
        
        elif ai_run_mode == "local_pc":

            screenshot = metadata.get('vm_response').get('screenshot')
            if not screenshot:
                raise InternalServerError("No screenshot available from Local PC")
            
            # ADD SCREENSHOT THUMBNAIL TO CHAT
            screenshot_message = {
                "id": str(uuid.uuid4()),
                "role": "assistant",
                "type": "image",
                "content": "Screenshot captured from Local PC",
                "timestamp": int(time.time() * 1000),
                "imageUrl": f"data:image/png;base64,{screenshot}" if not screenshot.startswith('data:') else screenshot
            }
            instance["messages"].append(screenshot_message)
            logger.info(f"Added Local PC screenshot thumbnail to chat for instance {run_instance_id}")
        
        # "local_vm"
        else:
            logger.info("[_dispatch] local_vm mode")
            
            screenshot = metadata.get('vm_response').get('screenshot')
            if not screenshot:
                raise InternalServerError("No screenshot available from Local VM")
            
            # ADD LOCAL VM STATUS MESSAGE TO CHAT
            vm_status_message = {
                "id": str(uuid.uuid4()),
                "role": "assistant",
                "type": "text",
                "content": "Local VM desktop captured via electron app",
                "timestamp": int(time.time() * 1000),
            }
            instance["messages"].append(vm_status_message)
            logger.info(f"Added Local VM status message to chat for instance {run_instance_id}")

            # ADD SCREENSHOT THUMBNAIL TO CHAT
            screenshot_message = {
                "id": str(uuid.uuid4()),
                "role": "assistant",
                "type": "image",
                "content": "Screenshot captured from Cloud PC",
                "timestamp": int(time.time() * 1000),
                "imageUrl": f"data:image/png;base64,{screenshot}" if not screenshot.startswith('data:') else screenshot
            }
            instance["messages"].append(screenshot_message)
            logger.info(f"Added Local VM screenshot thumbnail to chat for instance {run_instance_id}")

        # Loop control
        max_loops = 5  # avoid infinite loops between backend and ai run server
        loop_count = 0
        last_result = None
        
        try:
            while loop_count < max_loops:
                
                loop_count += 1
                logger.info(f"AI run {run_instance_id} dispatch loop, iteration {loop_count}")
                uia_data = {}

                # 2. Prepare payload for AI Run Server (using screenshot captured above)
                payload = {
                    "task_id": run_instance_id,
                    "user_id": str(instance.get("user_id")),  
                    "workflow_id": instance.get("workflowId"),
                    "query": query,
                    "screenshot": screenshot,
                    "uia_data": uia_data,
                }
                
                # 3. Call AI Run Server to get next action
                ai_run_response = call_external_ai_run_server(payload)
                
                if not ai_run_response:
                    logger.warning(f"AI Run Server failed to respond for instance {run_instance_id}. Stopping loop.")
                    break # Exit loop if server fails to respond

                # 4. Process response (includes executing PC actions and updating history)
                start_api_handler = ConsoleAiRunStartApi()
                result = start_api_handler._process_ai_run_response(ai_run_response, instance)
                last_result = result

                # 5. Check node_type to determine loop control
                node_type = ai_run_response.get("node_type")
                logger.info(f"AI run {run_instance_id} received node_type: {node_type}")

                # TODO: better handle nodetypes in ai run server if needed 
                if node_type in ["flow-control-node", "start", "end", "if-else", "loop", "loop-start", "knowledge-retrieval", "tools"]:
                    # Resend the same payload to continue to the next node
                    logger.info(f"Node type is '{node_type}'. Continuing loop for run {run_instance_id}.")
                    continue
                
                elif node_type in ["action-node", "gui-milestone"]:
                    # Normal flow: return to frontend to show progress and let it trigger continuation
                    logger.info(f"Node type is '{node_type}'. Returning result to frontend for run {run_instance_id}.")
                    
                    instance["status"] = "running"
                    instance["is_processing"] = True
                    
                    result_ai_run_response = {
                        "status": "success", 
                        "message": "Action processed successfully", 
                        "runInstanceId": run_instance_id, 
                        "generate_action": ai_run_response,
                        "continue_processing": True,
                        **result
                    }
                    with open("ai_run_console_api_output.txt", "a") as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} _dispatch\n{json.dumps(result_ai_run_response, indent=2)}\n")
                    return result_ai_run_response, 200

                elif node_type == "error":
                    # Error flow: stop the loop and report error
                    logger.error(f"Node type is '{node_type}'. Stopping loop for run {run_instance_id}.")
                    instance["is_processing"] = False
                    # The error message from the AI run response should be in the processed result
                    
                    response = {
                        "status": "error",
                        "message": f"AI run failed with error node.",
                        "runInstanceId": run_instance_id,
                        "generate_action": ai_run_response,
                        **result
                    }
                    with open("ai_run_console_api_output.txt", "a") as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} _dispatch\n{json.dumps(response, indent=2)}\n")
                    return response, 500
                
                else:
                    # Default/fallback behavior if node_type is missing or unknown
                    logger.warning(f"Unknown or missing node_type: '{node_type}'. Stopping loop for run {run_instance_id}.")
                    break
            

            if last_result:
                instance["status"] = "completed" if last_result.get("completed") else "running"
                instance["is_processing"] = not last_result.get("completed", False)
                response = {
                    "status": "success",
                    "message": "Input processed successfully",
                    "runInstanceId": run_instance_id, 
                    "generate_action": ai_run_response,
                    # "ai_run_response": last_result.get("response_data"),
                    **last_result
                }
                with open("ai_run_console_api_output.txt", "a") as f:
                    f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} _dispatch\n{json.dumps(response, indent=2)}\n")
                return response, 200
            else:
                # This case is hit if the server failed on the first attempt
                instance["is_processing"] = False
                response = {
                    "status": "error",
                    "message": "AI Run Server failed to respond"
                }
                with open("ai_run_console_api_output.txt", "a") as f:
                    f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} _dispatch\n{json.dumps(response, indent=2)}\n")
                return response, 500

        except Exception as e:
            instance["is_processing"] = False
            logger.error(f"Error dispatching input for instance {run_instance_id}: {e}", exc_info=True)
            raise InternalServerError(f"Failed to dispatch input: {e}")

class ConsoleAiRunStopApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, run_instance_id: str):
        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        logger.info(f"Stopping AI run instance {run_instance_id} locally.")
        instance["status"] = "stopped"
        instance["is_processing"] = False
        instance["updatedAt"] = int(time.time() * 1000)
        
        action_log = {"id": str(uuid.uuid4()),"role": "system","type": "status","content": "AI run stopped.","timestamp": int(time.time() * 1000)}
        instance["messages"].append(action_log)
        
        return {"status": "success", "message": "AI run stopped locally"}, 200

class ConsoleAiRunPauseApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, run_instance_id: str):
        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        logger.info(f"Pausing AI run instance {run_instance_id} locally.")
        instance["is_paused"] = True
        instance["updatedAt"] = int(time.time() * 1000)

        action_log = {"id": str(uuid.uuid4()),"role": "system","type": "status","content": "AI run paused.","timestamp": int(time.time() * 1000)}
        instance["messages"].append(action_log)
        
        return {"status": "success", "message": "AI run paused locally"}, 200

class ConsoleAiRunResumeApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self, run_instance_id: str):
        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        logger.info(f"Resuming AI run instance {run_instance_id} locally.")
        instance["is_paused"] = False
        instance["updatedAt"] = int(time.time() * 1000)
        
        action_log = {"id": str(uuid.uuid4()),"role": "system","type": "status","content": "AI run resumed.","timestamp": int(time.time() * 1000)}
        instance["messages"].append(action_log)
        
        return {"status": "success", "message": "AI run resumed locally"}, 200
            
class ConsoleAiRunStatusApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, run_instance_id: str):
        if run_instance_id not in ai_run_instances: raise NotFound("AI run instance not found")
        instance = ai_run_instances[run_instance_id]
        if instance.get("user_id") != current_user.id: raise NotFound("AI run instance not found")
        
        return {
            "status": instance["status"],
            "is_processing": instance.get("is_processing", False),
            "is_paused": instance.get("is_paused", False),
            "updatedAt": instance["updatedAt"]
        }

# Endpoints are registered in console/__init__.py to avoid duplication