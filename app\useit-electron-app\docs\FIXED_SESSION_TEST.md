# 固定RDP Session ID测试指南

## 概述

我们已经简化了RDP session管理，现在使用固定的session ID 1-16：

- **Local VM 1** → Session ID: `1`
- **Local VM 2** → Session ID: `2`  
- **Local VM 3** → Session ID: `3`
- **Local VM 4** → Session ID: `4`

## 实现特点

### 1. 固定Session ID
- 不再使用复杂的时间戳生成session ID
- 直接使用1-16的固定数字作为session ID
- Local VM 1永远使用session ID "1"，以此类推

### 2. 应用启动时预初始化
- 软件启动时自动初始化16个RDP session slot
- 只有打开的tab才会实际显示连接
- 预留了16个session供将来扩展使用

### 3. 简化的API
- `getPersistentRdpSession()` 现在直接返回固定的session ID
- 不再需要复杂的session状态管理
- session ID就是sessionIndex的字符串形式

## 测试步骤

### 1. 基本功能测试

1. **启动应用**
   - 检查控制台是否显示："Initializing 16 fixed RDP sessions..."
   - 确认VM启动成功

2. **打开Debug页面**
   - 点击"🔗 Open RDP Grid"
   - 观察控制台输出，应该看到：
     ```
     [Debug] Using fixed session ID 1 for UseIt-Dev-VM (index 1)
     [Debug] Using fixed session ID 2 for UseIt-Dev-VM (index 2)
     [Debug] Using fixed session ID 3 for UseIt-Dev-VM (index 3)
     [Debug] Using fixed session ID 4 for UseIt-Dev-VM (index 4)
     ```

3. **验证Session ID**
   - 检查RDP连接URL是否包含正确的sessionId参数
   - Local VM 1的URL应该包含`sessionId=1`
   - Local VM 2的URL应该包含`sessionId=2`

4. **验证mstsc.js Session复用**
   - 观察mstsc服务控制台输出，应该看到：
     ```
     [RDP Session] Creating new session: 1_172.26.148.45_UseIt
     [RDP Session] New session connected: 1_172.26.148.45_UseIt
     ```
   - 关闭tab后重新连接，应该看到：
     ```
     [RDP Session] Reusing existing session: 1_172.26.148.45_UseIt
     ```

### 2. 持久性测试

1. **连接到Local VM 1**
   - 在RDP session中运行一些程序（如记事本、计算器）
   - 记录当前运行的程序

2. **关闭并重新打开**
   - 关闭Local VM 1的tab
   - 重新点击"🔗 Open RDP Grid"
   - 再次连接到Local VM 1

3. **验证持久性**
   - 检查是否回到相同的session
   - 之前运行的程序是否仍在运行
   - Session ID是否仍然是"1"

### 3. 多Session测试

1. **同时打开多个session**
   - 连接到Local VM 1, 2, 3, 4
   - 在每个session中运行不同的程序

2. **验证Session隔离**
   - 确认每个session都有独立的桌面环境
   - 程序不会在session之间混淆

3. **Session状态检查**
   - 点击"📋 RDP Sessions"按钮
   - 查看所有16个session的状态

## 预期结果

### 控制台输出示例
```
[RDP Session] Initializing 16 fixed RDP sessions...
[RDP Session] Pre-creating session 1 for VM IP: *************
[RDP Session] Pre-creating session 2 for VM IP: *************
...
[RDP Session] All 16 RDP sessions initialized successfully

[RDP Session] Getting fixed session for UseIt-Dev-VM session 1
[RDP Session] Returning fixed session ID: 1
[MSTSC Service] Using fixed session ID: 1

[MSTSC Service] [RDP Session] Creating new session: 1_172.26.148.45_UseIt
[MSTSC Service] [RDP Session] New session connected: 1_172.26.148.45_UseIt
[MSTSC Service] [RDP Session] Client disconnected from session: 1_172.26.148.45_UseIt
[MSTSC Service] [RDP Session] Reusing existing session: 1_172.26.148.45_UseIt
```

### URL格式示例
```
http://localhost:9250/auto-connect.html?ip=*************&username=UseIt&password=123456&sessionId=1
http://localhost:9250/auto-connect.html?ip=*************&username=UseIt&password=123456&sessionId=2
http://localhost:9250/auto-connect.html?ip=*************&username=UseIt&password=123456&sessionId=3
http://localhost:9250/auto-connect.html?ip=*************&username=UseIt&password=123456&sessionId=4
```

## 故障排除

### 如果Session ID不固定
1. 检查`getFixedSessionId()`函数是否正确返回字符串
2. 确认`getPersistentRdpSession` IPC handler使用了新的逻辑
3. 查看控制台是否有错误信息

### 如果无法回到相同Session
1. 确认VM中的RDP Wrap配置正确
2. 检查mstsc.js是否正确传递sessionId参数
3. 验证VM的RDP服务状态

### 如果Session创建失败
1. 检查VM IP地址是否正确获取
2. 确认VM已启动并可访问
3. 查看网络连接状态

## 技术细节

### Session ID映射
- sessionIndex 1 → sessionId "1"
- sessionIndex 2 → sessionId "2"
- sessionIndex 3 → sessionId "3"
- sessionIndex 4 → sessionId "4"
- ...最多支持到16

### 关键函数
- `getFixedSessionId(sessionIndex)` - 返回固定的session ID字符串
- `initializeFixedRdpSessions()` - 应用启动时初始化所有session
- `getPersistentRdpSession` IPC handler - 返回固定session ID

这个简化的方案确保了每个Local VM tab都有固定的session ID，解决了RDP Wrap环境下的session管理问题。
