# This script creates a VM from an existing VHDX file (exported from Hyper-V)
# This script must be run as Administrator.

param(
    [Parameter(Mandatory=$true)]
    [string]$VHDXPath,
    
    [Parameter(Mandatory=$false)]
    [string]$VMName = "UseIt-Dev-VM",
    
    [Parameter(Mandatory=$false)]
    [string]$VMBasePath = "C:\Users\<USER>\Downloads\UseIt-VM",
    
    [Parameter(Mandatory=$false)]
    [int64]$VMMemory = 2GB,
    
    [Parameter(Mandatory=$false)]
    [int]$VMProcessors = 2,
    
    [Parameter(Mandatory=$false)]
    [string]$VMSwitchName = "Default Switch"
)

$ErrorActionPreference = 'Stop' # Exit script on first error

function Pause-And-Exit {
    param(
        [string]$Message = "Press Enter to exit"
    )
    Write-Host $Message
    Read-Host
    # Use exit code 0 for success, 1 for failure. The calling process can check this.
    if ($LASTEXITCODE -ne 0) {
        exit 1
    } else {
        exit 0
    }
}

try {
    Write-Host "INFO: --- VM Creation from VHDX Started ---"
    Write-Host "INFO: VM Name: $VMName"
    Write-Host "INFO: VM Path: $VMBasePath"
    Write-Host "INFO: Source VHDX: $VHDXPath"
    Write-Host "INFO: Memory: $([math]::Round($VMMemory/1GB, 2)) GB"
    Write-Host "INFO: Processors: $VMProcessors"
    Write-Host "INFO: Network Switch: $VMSwitchName"
    Write-Host "---------------------------------"

    # Check if VM already exists
    if (Get-VM -Name $VMName -ErrorAction SilentlyContinue) {
        Write-Host "INFO: VM '$VMName' already exists. Exiting."
        Pause-And-Exit "VM already exists. Press Enter to exit."
    }

    # --- Pre-flight checks ---
    if (-not (Test-Path -Path $VHDXPath)) {
        throw "Source VHDX file not found at '$VHDXPath'. Please check the path."
    }
    
    # Check if the file is actually a VHDX file
    $fileExtension = [System.IO.Path]::GetExtension($VHDXPath).ToLower()
    if ($fileExtension -ne ".vhdx") {
        throw "The specified file '$VHDXPath' is not a VHDX file. Expected extension: .vhdx"
    }
    
    if (-not (Get-VMSwitch -Name $VMSwitchName -ErrorAction SilentlyContinue)) {
        throw "Network switch '$VMSwitchName' not found. Please ensure Hyper-V is set up correctly."
    }

    # --- Directory Creation ---
    Write-Host "INFO: Creating VM directory..."
    New-Item -ItemType Directory -Path $VMBasePath -ErrorAction SilentlyContinue | Out-Null
    
    # --- Copy VHDX to VM directory ---
    $destinationVHDXPath = Join-Path $VMBasePath "$VMName.vhdx"
    Write-Host "INFO: Copying VHDX file to VM directory..."
    Write-Host "INFO: Source: $VHDXPath"
    Write-Host "INFO: Destination: $destinationVHDXPath"
    
    # Check if destination already exists
    if (Test-Path -Path $destinationVHDXPath) {
        Write-Host "WARNING: Destination VHDX already exists. Removing it..."
        Remove-Item -Path $destinationVHDXPath -Force
    }
    
    Copy-Item -Path $VHDXPath -Destination $destinationVHDXPath -Force
    Write-Host "INFO: VHDX file copied successfully."

    # --- VM Creation and Configuration ---
    Write-Host "INFO: Creating new VM '$VMName'..."
    New-VM -Name $VMName -MemoryStartupBytes $VMMemory -Generation 2 -VHDPath $destinationVHDXPath -Path $VMBasePath

    Write-Host "INFO: Configuring VM settings..."
    Set-VM -Name $VMName -ProcessorCount $VMProcessors
    Set-VMMemory -VMName $VMName -StartupBytes $VMMemory -DynamicMemoryEnabled $false

    Write-Host "INFO: Connecting network adapter..."
    Connect-VMNetworkAdapter -VMName $VMName -SwitchName $VMSwitchName

    Write-Host "INFO: Configuring firmware settings..."
    # For VHDX from exported VM, we typically want to boot from the hard drive
    $hardDrive = Get-VMHardDiskDrive -VMName $VMName
    Set-VMFirmware -VMName $VMName -FirstBootDevice $hardDrive
    Set-VMFirmware -VMName $VMName -EnableSecureBoot Off

    Write-Host "SUCCESS: VM '$VMName' created successfully from VHDX."
    Write-Host "The VM is ready to start. It should boot directly from the imported VHDX."

} catch {
    Write-Host "ERROR: An error occurred: $($_.Exception.Message)"
    Write-Host "ERROR: At $($_.InvocationInfo.ScriptName):$($_.InvocationInfo.ScriptLineNumber)"
    $LASTEXITCODE = 1 # Set a non-zero exit code to indicate failure
} finally {
    Pause-And-Exit
}
