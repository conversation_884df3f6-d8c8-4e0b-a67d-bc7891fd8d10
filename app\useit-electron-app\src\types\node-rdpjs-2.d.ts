declare module 'node-rdpjs-2' {
  interface RdpClientConfig {
    domain?: string;
    userName: string;
    password: string;
    enablePerf?: boolean;
    autoLogin?: boolean;
    decompress?: boolean;  // 控制是否启用bitmap压缩
    screen?: {
      width: number;
      height: number;
    };
    locale?: string;
    logLevel?: string;
    // 剪贴板和设备重定向支持
    enableClipboard?: boolean;  // 启用剪贴板共享 (默认: true)
    enableLocalPrinters?: boolean;  // 启用本地打印机 (默认: true)
    enableLocalCOMPorts?: boolean;  // 启用本地COM端口 (默认: false)
    enableSmartCards?: boolean;  // 启用智能卡 (默认: true)
    enableDrives?: string;  // 启用本地驱动器，用分号分隔或使用 '*' (默认: '')
    enablePlugAndPlayDevices?: string;  // 启用即插即用设备 (默认: '')
    // 音频设置
    audioPlayMode?: 'local' | 'remote' | 'none';  // 音频播放模式 (默认: 'local')
    audioCaptureMode?: boolean;  // 启用音频捕获 (默认: false)
    // 显示设置
    showWallpaper?: boolean;  // 显示壁纸 (默认: false)
    fontSmoothing?: boolean;  // 字体平滑 (默认: false)
    desktopComposition?: boolean;  // 桌面合成/Aero效果 (默认: false)
    showDraggedWindow?: boolean;  // 拖拽时显示完整窗口 (默认: false)
    showMenuAnimations?: boolean;  // 菜单动画 (默认: false)
    showThemes?: boolean;  // 主题 (默认: true)
    showBlinkingCursor?: boolean;  // 闪烁光标 (默认: true)
  }

  interface BitmapData {
    destTop: number;
    destLeft: number;
    destBottom: number;
    destRight: number;
    width: number;
    height: number;
    bitsPerPixel: number;
    isCompress: boolean;
    data: Buffer;
  }

  interface RdpClient {
    connect(ip: string, port?: number): RdpClient;
    sendPointerEvent(x: number, y: number, button: number, isPressed: boolean): void;
    sendKeyEventScancode(code: number, isPressed: boolean): void;
    sendKeyEventUnicode(code: number, isPressed: boolean): void;
    sendWheelEvent(x: number, y: number, step: number, isNegative: boolean, isHorizontal: boolean): void;
    close(): void;
    removeAllListeners(): void;
    on(event: 'connect', callback: () => void): RdpClient;
    on(event: 'ready', callback: () => void): RdpClient;
    on(event: 'close', callback: () => void): RdpClient;
    on(event: 'error', callback: (err: Error) => void): RdpClient;
    on(event: 'bitmap', callback: (bitmap: BitmapData) => void): RdpClient;
  }

  function createClient(config: RdpClientConfig): RdpClient;

  export = {
    createClient
  };
} 