<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Screen Recorder</title>
    <link rel="stylesheet" href="./vendor/css/photon.css">
    <link rel="stylesheet" href="./styles/index.css">
</head>
<body>
    <div class="header-arrow"></div>
    <div class="window">
        <header class="toolbar toolbar-header">
            <div class="toolbar-actions">
                <h1 class="title">Record your screen</h1>
            </div>
        </header>

        <div class="window-content">
            <div class="pane">
                <div class="sources-container">
                    <div id="sourcesList" class="sources-grid"></div>
                </div>

                <div class="toolbar-actions text-center" style="margin: 10px 0;">
                    <div class="btn-group">
                        <button class="btn btn-default" id="startBtn" disabled>
                            <span class="icon icon-record"></span>
                            Start
                        </button>
                        <button class="btn btn-default" id="stopBtn" disabled>
                            <span class="icon icon-stop"></span>
                            Stop
                        </button>
                    </div>
                    <button class="btn btn-default" id="refreshSourcesBtn">
                        <span class="icon icon-arrows-ccw"></span>
                    </button>
                </div>

                <div class="mini-status">
                    <span id="recordingTime" class="recording-time">0</span>秒
                    <div id="status" class="status-text"></div>
                </div>
            </div>
        </div>
    </div>

    <dialog id="processingDialog" class="dialog">
        <div class="dialog-body">
            <p>Processing video file, please wait...</p>
        </div>
    </dialog>

    <script src="./renderer.js"></script>

</body>
</html> 