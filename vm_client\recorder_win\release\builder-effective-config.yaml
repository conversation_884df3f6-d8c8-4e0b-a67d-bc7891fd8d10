directories:
  output: release
  buildResources: build
appId: com.example.electron-screen-recorder
productName: Screen Recorder
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: assets/video-recording.png
  certificateFile: null
  certificatePassword: <stripped sensitive data>
  verifyUpdateCodeSignature: false
forceCodeSigning: false
files:
  - filter:
      - '**/*'
      - '!node_modules/ffmpeg-static${/*}'
      - '!node_modules/ffprobe-static${/*}'
extraResources:
  - from: node_modules/ffmpeg-static/ffmpeg.exe
    to: ./ffmpeg.exe
  - from: node_modules/ffprobe-static/bin/win32/x64/ffprobe.exe
    to: ./ffprobe.exe
  - from: ./InputListener.exe
    to: ./InputListener.exe
  - from: ./_internal
    to: ./_internal
asar: true
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 28.3.3
