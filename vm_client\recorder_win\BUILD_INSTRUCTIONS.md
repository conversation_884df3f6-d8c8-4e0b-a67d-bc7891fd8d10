# Recorder 编译说明

## 前置要求

### 1. 安装 Node.js
- 下载并安装 Node.js (推荐版本 18.x 或更高)
- 验证安装: `node --version` 和 `npm --version`

### 2. 安装 Python (如果需要重新编译Python组件)
- Python 3.11 (推荐)
- 安装 PyInstaller: `pip install pyinstaller`

## 编译步骤

### 第一步: 安装依赖

在 `recorder_win` 目录下运行：

```bash
cd recorder_win
npm install
```

**注意**: 如果之前已经安装过依赖，建议清理后重新安装：

```bash
# 清理node_modules和package-lock.json
rm -rf node_modules package-lock.json
# 或者在Windows上使用
rmdir /s node_modules
del package-lock.json

# 重新安装
npm install
```

### 第二步: 验证Python组件 (可选)

如果 `InputListener.exe` 和 `_internal` 文件夹已存在，可以跳过此步骤。

如果需要重新编译Python组件：

```bash
# 安装Python依赖
pip install flask waitress pynput psutil pywin32 cryptography

# 编译listener.py为可执行文件
pyinstaller listener.spec
```

### 第三步: 验证依赖 (推荐)

```bash
node verify_dependencies.js
```

这将检查所有必需的依赖和文件是否正确安装。

### 第四步: 开发模式运行 (测试)

```bash
npm start
```

这将启动开发版本，用于测试新添加的API接口。

### 第五步: 编译生产版本

#### One-Click Build (Recommended)
```bash
build.bat
```

This script will:
- Auto-install pnpm if needed
- Clean environment
- Install all dependencies (60+ packages)
- Build the application
- Optionally test APIs

## 编译输出

编译完成后，可执行文件将位于：
- `release/` 目录下
- 包含安装程序 (.exe) 和便携版本

## 测试新功能

### 1. 启动应用
运行编译后的应用或使用 `npm start`

### 2. 测试API接口
```bash
# 安装requests库 (如果没有)
pip install requests

# 运行测试脚本
python test_api.py
```

### 3. 手动测试
```bash
# 显示窗口
curl -X POST http://localhost:3000/showWindow

# 隐藏窗口
curl -X POST http://localhost:3000/hideWindow
```

## 新增功能说明

已添加两个新的API接口：

1. **显示窗口**: `POST /showWindow`
   - 从托盘显示recorder窗口
   - 窗口会出现在托盘图标附近

2. **隐藏窗口**: `POST /hideWindow`  
   - 隐藏recorder窗口到托盘
   - 应用继续在后台运行

## 故障排除

### 常见问题

1. **"cannot find module" errors (body-parser, depd, ms, es-errors, etc.)**
   ```bash
   # Use the unified build script
   build.bat
   ```

2. **编译失败 - 缺少依赖**
   ```bash
   # 验证依赖
   node verify_dependencies.js

   # 重新安装
   npm install --force
   ```

3. **Python组件缺失**
   - 确保 `InputListener.exe` 存在
   - 如果缺失，使用 PyInstaller 重新编译

4. **API测试失败**
   - 确保应用正在运行
   - 检查端口3000是否被占用
   - 查看控制台错误信息

5. **权限问题**
   - Windows可能需要管理员权限
   - 确保防火墙允许应用运行

### 日志查看

- 应用日志: 查看控制台输出
- Python组件日志: `~/Downloads/record_save/server.log`

## 项目结构

```
recorder_win/
├── main.js              # 主进程文件 (已修改)
├── package.json         # 项目配置
├── InputListener.exe    # Python编译的监听器
├── _internal/           # Python依赖文件
├── assets/              # 图标资源
├── test_api.py          # API测试脚本 (新增)
├── API_USAGE.md         # API使用说明 (新增)
└── BUILD_INSTRUCTIONS.md # 本文件 (新增)
```

## 部署

编译完成的应用可以：
1. 直接运行便携版
2. 使用安装程序安装到系统
3. 分发给其他用户使用

应用会自动创建托盘图标，支持通过API或托盘菜单控制显示/隐藏。
