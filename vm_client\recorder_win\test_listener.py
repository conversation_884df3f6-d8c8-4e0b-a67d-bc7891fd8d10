#!/usr/bin/env python3
"""
测试InputListener.exe是否正常工作
"""

import subprocess
import time
import requests
import os

def test_listener_exe():
    print("=" * 50)
    print("测试 InputListener.exe")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists('InputListener.exe'):
        print("❌ InputListener.exe 不存在")
        return False
    
    print("✅ InputListener.exe 存在")
    
    # 尝试启动
    print("\n启动 InputListener.exe...")
    try:
        process = subprocess.Popen(
            ['InputListener.exe'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("✅ 进程已启动，PID:", process.pid)
        
        # 等待几秒让服务启动
        print("等待服务启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 进程仍在运行")
        else:
            print("❌ 进程已退出")
            stdout, stderr = process.communicate()
            print("标准输出:", stdout)
            print("错误输出:", stderr)
            return False
        
        # 测试端口4000
        print("\n测试端口4000连接...")
        try:
            response = requests.get('http://localhost:4000/health', timeout=5)
            if response.status_code == 200:
                print("✅ 端口4000响应正常")
                print("响应:", response.json())
                
                # 测试新的API
                print("\n测试新的API接口...")
                
                # 测试命令执行
                test_data = {
                    "command": "echo Test Command",
                    "timeout": 5
                }
                
                response = requests.post(
                    'http://localhost:4000/executeCommand',
                    json=test_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    print("✅ executeCommand 接口工作正常")
                    print("响应:", response.json())
                else:
                    print("❌ executeCommand 接口失败")
                    print("状态码:", response.status_code)
                    print("响应:", response.text)
                
            else:
                print("❌ 端口4000响应异常，状态码:", response.status_code)
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到端口4000")
            return False
        except Exception as e:
            print("❌ 测试端口4000时出错:", e)
            return False
        
        # 终止进程
        print("\n终止进程...")
        process.terminate()
        process.wait(timeout=5)
        print("✅ 进程已终止")
        
        return True
        
    except Exception as e:
        print("❌ 启动进程时出错:", e)
        return False

def test_direct_python():
    print("\n" + "=" * 50)
    print("测试直接运行 Python 脚本")
    print("=" * 50)
    
    if not os.path.exists('listener.py'):
        print("❌ listener.py 不存在")
        return False
    
    print("✅ listener.py 存在")
    
    try:
        print("\n启动 listener.py...")
        process = subprocess.Popen(
            ['python', 'listener.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("✅ Python进程已启动，PID:", process.pid)
        
        # 等待几秒让服务启动
        print("等待服务启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Python进程仍在运行")
        else:
            print("❌ Python进程已退出")
            stdout, stderr = process.communicate()
            print("标准输出:", stdout)
            print("错误输出:", stderr)
            return False
        
        # 测试端口4000
        print("\n测试端口4000连接...")
        try:
            response = requests.get('http://localhost:4000/health', timeout=5)
            if response.status_code == 200:
                print("✅ Python版本端口4000响应正常")
                print("响应:", response.json())
            else:
                print("❌ Python版本端口4000响应异常")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Python版本无法连接到端口4000")
            return False
        
        # 终止进程
        print("\n终止Python进程...")
        process.terminate()
        process.wait(timeout=5)
        print("✅ Python进程已终止")
        
        return True
        
    except Exception as e:
        print("❌ 启动Python进程时出错:", e)
        return False

if __name__ == '__main__':
    print("诊断 InputListener 服务问题")
    
    # 测试exe版本
    exe_works = test_listener_exe()
    
    # 测试Python版本
    python_works = test_direct_python()
    
    print("\n" + "=" * 50)
    print("诊断结果")
    print("=" * 50)
    print(f"InputListener.exe: {'✅ 正常' if exe_works else '❌ 异常'}")
    print(f"Python listener.py: {'✅ 正常' if python_works else '❌ 异常'}")
    
    if not exe_works and python_works:
        print("\n建议: InputListener.exe编译有问题，建议重新编译")
    elif not exe_works and not python_works:
        print("\n建议: listener.py代码有问题，检查依赖和语法")
    elif exe_works:
        print("\n建议: InputListener.exe工作正常，检查主程序是否正确启动了它")
