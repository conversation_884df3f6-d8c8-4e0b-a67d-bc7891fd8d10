import { spawn, ChildProcess, exec } from 'child_process'
import path from 'path'
import { app } from 'electron'
import { existsSync } from 'fs'
import net from 'net'

let mstscProcess: ChildProcess | null = null
let currentPort: number = 9250 // 默认端口，但会动态分配

/**
 * 清理占用指定端口的进程
 */
async function killProcessOnPort(port: number): Promise<void> {
  return new Promise((resolve) => {
    if (process.platform === 'win32') {
      exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve()
          return
        }

        const lines = stdout.split('\n')
        const pids = new Set<string>()
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/)
          if (parts.length >= 5 && parts[1].includes(`:${port}`)) {
            pids.add(parts[4])
          }
        })

        if (pids.size === 0) {
          resolve()
          return
        }

        let killedCount = 0
        const totalPids = pids.size

        pids.forEach(pid => {
          // 先检查进程是否是我们的node进程
          exec(`tasklist /FI "PID eq ${pid}" /FO CSV`, (taskError, taskOutput) => {
            let shouldKill = false
            if (!taskError && taskOutput.includes('node.exe')) {
              shouldKill = true
            }

            if (shouldKill) {
              exec(`taskkill /F /PID ${pid}`, (killError) => {
                killedCount++
                if (killError) {
                  console.log(`[MSTSC Service] Failed to kill PID ${pid}:`, killError.message)
                } else {
                  console.log(`[MSTSC Service] Killed node process ${pid} on port ${port}`)
                }

                if (killedCount === totalPids) {
                  setTimeout(resolve, 1000) // 增加等待时间
                }
              })
            } else {
              killedCount++
              console.log(`[MSTSC Service] Skipped non-node process ${pid}`)
              if (killedCount === totalPids) {
                setTimeout(resolve, 1000)
              }
            }
          })
        })

      })
    } else {
      // Unix系统
      exec(`lsof -ti:${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve()
          return
        }

        const pids = stdout.trim().split('\n').filter(Boolean)
        if (pids.length === 0) {
          resolve()
          return
        }

        let killedCount = 0
        pids.forEach(pid => {
          exec(`kill -9 ${pid}`, (killError) => {
            killedCount++
            if (killError) {
              console.log(`[MSTSC Service] Failed to kill PID ${pid}:`, killError.message)
            } else {
              console.log(`[MSTSC Service] Killed process ${pid} on port ${port}`)
            }
            
            if (killedCount === pids.length) {
              setTimeout(resolve, 500)
            }
          })
        })
      })
    }
  })
}

/**
 * 检测端口是否可用
 */
async function isPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer()
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(true)
      })
      server.close()
    })
    
    server.on('error', () => {
      resolve(false)
    })
  })
}

/**
 * 找到一个可用的端口
 */
async function findAvailablePort(startPort: number = 9250, maxAttempts: number = 50): Promise<number> {
  for (let i = 0; i < maxAttempts; i++) {
    const port = startPort + i
    const available = await isPortAvailable(port)
    if (available) {
      console.log(`[MSTSC Service] Found available port: ${port}`)
      return port
    }
  }
  throw new Error(`No available port found in range ${startPort}-${startPort + maxAttempts - 1}`)
}

/**
 * 获取mstsc.js的正确路径
 */
function getMstscPath(): string {
  // 在开发模式下，使用源代码路径
  if (process.env.NODE_ENV === 'development') {
    // 从编译后的位置 node_modules/.dev/main/ 回到项目根目录
    const projectRoot = path.resolve(__dirname, '../../../')
    return path.join(projectRoot, 'src/main/mstsc')
  } else {
    // 在生产模式下，mstsc.js应该在同级目录
    return path.join(__dirname, 'mstsc')
  }
}

/**
 * 启动mstsc.js服务
 */
export async function startMstscService(): Promise<void> {
  if (mstscProcess) {
    console.log('[MSTSC Service] Already running')
    return
  }

  // 清理可能占用端口的旧进程
  console.log('[MSTSC Service] Cleaning up old processes...')
  for (let port = 9250; port < 9300; port++) {
    await killProcessOnPort(port)
  }

  // 先找到可用端口
  try {
    currentPort = await findAvailablePort()
    console.log(`[MSTSC Service] Will use port: ${currentPort}`)
  } catch (error) {
    console.error('[MSTSC Service] Failed to find available port:', error)
    throw error
  }

  return new Promise((resolve, reject) => {
    const mstscPath = getMstscPath()
    const serverPath = path.join(mstscPath, 'server.js')

    console.log('[MSTSC Service] Starting mstsc.js service...')
    console.log('[MSTSC Service] NODE_ENV:', process.env.NODE_ENV)
    console.log('[MSTSC Service] __dirname:', __dirname)
    console.log('[MSTSC Service] mstscPath:', mstscPath)
    console.log('[MSTSC Service] serverPath:', serverPath)

    // 检查路径是否存在
    if (!existsSync(mstscPath)) {
      const error = new Error(`mstsc.js path does not exist: ${mstscPath}`)
      console.error('[MSTSC Service]', error.message)
      
      // 尝试备用路径
      const fallbackPath = path.resolve(__dirname, '../../../src/main/mstsc')
      console.log('[MSTSC Service] Trying fallback path:', fallbackPath)
      
      if (existsSync(fallbackPath)) {
        console.log('[MSTSC Service] Using fallback path')
        const fallbackServerPath = path.join(fallbackPath, 'server.js')
        return startMstscWithPath(fallbackPath, fallbackServerPath, resolve, reject)
      }
      
      reject(error)
      return
    }

    if (!existsSync(serverPath)) {
      const error = new Error(`server.js does not exist: ${serverPath}`)
      console.error('[MSTSC Service]', error.message)
      reject(error)
      return
    }

    startMstscWithPath(mstscPath, serverPath, resolve, reject)
  })
}

/**
 * 使用指定路径启动mstsc服务
 */
function startMstscWithPath(mstscPath: string, serverPath: string, resolve: () => void, reject: (error: Error) => void): void {
  console.log('[MSTSC Service] Paths verified, starting server directly...')

  // 直接启动服务器，不先安装依赖（因为依赖已经存在）
  mstscProcess = spawn('node', [serverPath], {
    cwd: mstscPath,
    stdio: ['ignore', 'pipe', 'pipe'], // 明确指定stdio
    shell: true,
    env: {
      ...process.env,
      PORT: currentPort.toString()
    }
  })

  let serviceStarted = false

  mstscProcess.stdout?.on('data', (data) => {
    const output = data.toString().trim()
    console.log('[MSTSC Service]', output)
    
    // 检查服务是否启动成功
    if (!serviceStarted && (output.includes('listening') || output.includes('started'))) {
      serviceStarted = true
      console.log(`[MSTSC Service] Service confirmed started on port ${currentPort}`)
      resolve()
    }
  })

  mstscProcess.stderr?.on('data', (data) => {
    const error = data.toString().trim()
    console.error('[MSTSC Service Error]', error)
    
    // 如果是端口占用错误，尝试重新启动
    if (!serviceStarted && error.includes('EADDRINUSE')) {
      console.log('[MSTSC Service] Port conflict detected, will retry with new port')
      mstscProcess?.kill()
      mstscProcess = null
      
      // 重新尝试启动
      setTimeout(async () => {
        try {
          await startMstscService()
          resolve()
        } catch (retryError) {
          reject(retryError as Error)
        }
      }, 1000)
      return
    }
    
    if (!serviceStarted) {
      reject(new Error(`Service startup error: ${error}`))
    }
  })

  mstscProcess.on('error', (error) => {
    console.error('[MSTSC Service] Process error:', error)
    mstscProcess = null
    if (!serviceStarted) {
      reject(error)
    }
  })

  mstscProcess.on('exit', (code, signal) => {
    console.log('[MSTSC Service] Process exited with code:', code, 'signal:', signal)
    mstscProcess = null
    if (!serviceStarted && code !== 0) {
      reject(new Error(`Service exited with code ${code}`))
    }
  })

  // 给服务器时间启动 - 如果5秒内没有确认启动则认为成功
  setTimeout(() => {
    if (!serviceStarted) {
      console.log(`[MSTSC Service] Timeout waiting for startup confirmation, assuming success on port ${currentPort}`)
      serviceStarted = true
      resolve()
    }
  }, 5000)
}

/**
 * 停止mstsc.js服务
 */
export function stopMstscService(): void {
  if (mstscProcess) {
    console.log('[MSTSC Service] Stopping service...')
    mstscProcess.kill('SIGTERM')
    
    // 等待进程完全退出
    setTimeout(() => {
      if (mstscProcess && !mstscProcess.killed) {
        console.log('[MSTSC Service] Force killing process...')
        mstscProcess.kill('SIGKILL')
      }
      mstscProcess = null
    }, 2000)
  }
}

/**
 * 获取mstsc.js服务状态
 */
export function getMstscServiceStatus(): { running: boolean; port: number; url: string } {
  return {
    running: mstscProcess !== null,
    port: currentPort,
    url: `http://localhost:${currentPort}`
  }
}

/**
 * 获取当前使用的端口
 */
export function getCurrentPort(): number {
  return currentPort
}

/**
 * 获取自动连接URL
 */
export function getMstscAutoConnectUrl(ip: string, username: string = 'UseIt', password: string = '123456', domain: string = '', sessionId?: string): string {
  const params = new URLSearchParams({
    ip,
    username,
    password,
    domain
  })

  // Add fixed sessionId (1-16) if provided
  if (sessionId) {
    params.set('sessionId', sessionId)
    console.log(`[MSTSC Service] Using fixed session ID: ${sessionId}`)
  }

  return `http://localhost:${currentPort}/auto-connect.html?${params.toString()}`
}