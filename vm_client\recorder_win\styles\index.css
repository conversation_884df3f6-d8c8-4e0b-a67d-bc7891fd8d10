:root {
  --background-color: #f5f5f4;
  --text-color: #333;
  --window-border-radius: 6px;
  --font-size: 14px;
}

body {
  background: transparent;
}

.window {
  position: absolute;
  top: 5px;
  width: 100%;
  background: white;
  border-radius: var(--window-border-radius);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.toolbar-header {
  border-top-left-radius: var(--window-border-radius);
  border-top-right-radius: var(--window-border-radius);
  -webkit-app-region: drag; /* 允许拖动窗口 */
}

.toolbar-header .btn {
  -webkit-app-region: no-drag; /* 按钮不参与拖动 */
}

.window-content {
  background-color: var(--background-color);
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  padding: 10px;
}

.source-item {
  background: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.source-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.source-item img {
  width: 100%;
  height: auto;
  display: block;
}

.source-info {
  padding: 8px;
  text-align: center;
  font-size: 12px;
}

.mini-status {
  text-align: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
  margin-top: 10px;
}

.recording-time {
  font-size: 20px;
  font-weight: bold;
  color: #2196F3;
}

.status-text {
  font-size: 12px;
  margin-top: 5px;
  color: #666;
}

.text-center {
  text-align: center;
}

/* 添加成功和错误状态样式 */
.success {
  color: #4CAF50;
}

.error {
  color: #F44336;
} 