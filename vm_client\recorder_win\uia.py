import time
import ctypes
import os
import datetime
from ctypes import windll, wintypes
from uiautomation import uiautomation as auto

# 定义Windows API函数
GetCursorPos = windll.user32.GetCursorPos
GetCursorPos.argtypes = [ctypes.POINTER(wintypes.POINT)]
GetAsyncKeyState = windll.user32.GetAsyncKeyState
VK_LBUTTON = 0x01

# 创建日志目录
log_dir = "uia_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 创建新的日志文件
def create_log_file():
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_path = os.path.join(log_dir, f"uia_log_{timestamp}.txt")
    return open(log_path, "w", encoding="utf-8")

def get_clicked_element():
    last_click_time = 0
    click_cooldown = 0.03  # 减少冷却时间以捕获更多快速点击
    last_key_state = 0
    was_pressed = False  # 跟踪上一帧鼠标是否按下
    ignored_clicks = 0
    processed_clicks = 0
    missed_elements = 0
    log_file = create_log_file()
    
    log_file.write("==== UIA点击检测日志 ====\n")
    log_file.write(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
    log_file.write(f"点击冷却时间: {click_cooldown}秒\n\n")
    log_file.flush()
    
    try:
        while True:
            try:
                current_time = time.time()
                # 获取当前鼠标位置（用于记录）
                pt = wintypes.POINT()
                GetCursorPos(ctypes.byref(pt))
                
                # 检测左键点击
                key_state = GetAsyncKeyState(VK_LBUTTON)
                is_pressed = (key_state & 0x8000) != 0
                key_state_changed = key_state != last_key_state
                
                # 记录键状态变化
                if key_state_changed:
                    log_file.write(f"时间: {datetime.datetime.now().strftime('%H:%M:%S.%f')} - "
                                  f"键状态变化: {last_key_state} -> {key_state} (pressed: {is_pressed})\n")
                    log_file.flush()
                
                # 只在鼠标从未按下变为按下的瞬间识别为点击
                if is_pressed and not was_pressed:
                    time_since_last = current_time - last_click_time
                    log_file.write(f"点击事件: 坐标({pt.x}, {pt.y}) 距上次: {time_since_last:.4f}秒\n")
                    
                    # 判断是否满足冷却时间
                    if time_since_last >= click_cooldown:
                        processed_clicks += 1
                        log_file.write(f"开始处理点击 [{processed_clicks}] (通过冷却检查)\n")
                        
                        # 获取点击位置
                        GetCursorPos(ctypes.byref(pt))
                        log_file.write(f"实际捕获位置: ({pt.x}, {pt.y})\n")
                        
                        # 最小延迟以确保元素状态稳定
                        time.sleep(0.005)  # 减少稳定延迟时间
                        
                        # 获取点击位置的元素
                        start_element_time = time.time()
                        element = auto.ControlFromPoint(pt.x, pt.y)
                        element_time = time.time() - start_element_time
                        
                        if element:
                            log_file.write(f"元素获取成功 (耗时: {element_time:.4f}秒)\n")
                            log_file.write(f"  Name: {element.Name}\n")
                            log_file.write(f"  ControlType: {element.ControlTypeName}\n")
                            
                            # 打印到控制台
                            print("\n=== 点击元素信息 ===")
                            print(f"位置: ({pt.x}, {pt.y})")
                            print(f"Name: {element.Name}")
                            print(f"ControlType: {element.ControlTypeName}")
                            
                            # 尝试获取更多有用的属性 (有选择地获取)
                            if hasattr(element, 'GetValuePattern') and element.GetValuePattern():
                                try:
                                    value = element.GetValuePattern().Value
                                    log_file.write(f"  Value: {value}\n")
                                    print(f"Value: {value}")
                                except Exception as e:
                                    log_file.write(f"  Value获取错误: {str(e)}\n")
                                
                            if hasattr(element, 'GetLegacyIAccessiblePattern') and element.GetLegacyIAccessiblePattern():
                                try:
                                    acc_name = element.GetLegacyIAccessiblePattern().Name
                                    log_file.write(f"  LegacyIAccessible Name: {acc_name}\n")
                                    print(f"LegacyIAccessible Name: {acc_name}")
                                except Exception as e:
                                    log_file.write(f"  LegacyIAccessible Name获取错误: {str(e)}\n")
                        else:
                            missed_elements += 1
                            log_file.write(f"元素获取失败 [累计: {missed_elements}] (耗时: {element_time:.4f}秒)\n")
                            print(f"\n=== 点击未检测到元素 ({missed_elements}) ===")
                            print(f"位置: ({pt.x}, {pt.y})")
                        
                        log_file.write(f"点击处理完成\n\n")
                        log_file.flush()
                        last_click_time = current_time
                    else:
                        ignored_clicks += 1
                        log_file.write(f"忽略点击 [{ignored_clicks}] (冷却中: {time_since_last:.4f}/{click_cooldown}秒)\n\n")
                        log_file.flush()
                
                # 更新鼠标状态
                was_pressed = is_pressed
                last_key_state = key_state
                time.sleep(0.002)  # 进一步减少休眠时间，提高检测灵敏度
                
            except KeyboardInterrupt:
                log_file.write("\n监听被用户中断\n")
                log_file.flush()
                print("\n监听已停止")
                break
            except Exception as e:
                error_msg = f"发生错误: {str(e)}"
                log_file.write(f"{error_msg}\n")
                log_file.flush()
                print(error_msg)
                continue
    finally:
        # 写入统计数据
        log_file.write("\n=== 统计信息 ===\n")
        log_file.write(f"已处理点击: {processed_clicks}\n")
        log_file.write(f"忽略的点击: {ignored_clicks}\n")
        log_file.write(f"未检测到元素: {missed_elements}\n")
        log_file.write(f"结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
        log_file.close()

if __name__ == "__main__":
    print(f"启动无权限监听器 (Ctrl+C 退出)...")
    print(f"日志保存在 '{os.path.abspath(log_dir)}' 目录下")
    print("请点击要检测的元素...")
    get_clicked_element()