import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from 'renderer/components/ui/button';
import { TitleBar } from 'renderer/components/title-bar';

export function DebugScreen() {
  const navigate = useNavigate();
  const [output, setOutput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [customIp, setCustomIp] = useState('*************');

  // VM Client states
  const [vmClientCommand, setVmClientCommand] = useState('echo Hello from VM Client');
  const [vmClientFilePath, setVmClientFilePath] = useState('C:\\temp\\test_file.txt');
  const [vmClientFileContent, setVmClientFileContent] = useState('Hello World from Electron App!');

  // RDP Grid State
  const [showRdpGrid, setShowRdpGrid] = useState(false);
  const [selectedRdpView, setSelectedRdpView] = useState<number | null>(null);
  const [rdpConnections, setRdpConnections] = useState<Array<{
    id: number
    name: string
    url: string
    status: 'connecting' | 'connected' | 'error'
    vmName: string
    sessionId?: string
  }>>([
    { id: 1, name: 'UseIt-Dev-VM', url: '', status: 'connecting', vmName: 'UseIt-Dev-VM' },
    { id: 2, name: 'Local VM 2', url: '', status: 'connecting', vmName: 'UseIt-Dev-VM-2' },
    { id: 3, name: 'Local VM 3', url: '', status: 'connecting', vmName: 'UseIt-Dev-VM-3' },
    { id: 4, name: 'Local VM 4', url: '', status: 'connecting', vmName: 'UseIt-Dev-VM-4' }
  ]);

  // Persistent iframe refs to maintain connections
  const iframeRefs = useRef<{ [key: number]: HTMLIFrameElement | null }>({});
  const gridIframeRefs = useRef<{ [key: number]: HTMLIFrameElement | null }>({});

  // Helper function to log results
  const logResult = (operation: string, result: any) => {
    const timestamp = new Date().toLocaleTimeString();
    const resultStr = typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result);
    setOutput(prev => `[${timestamp}] ${operation}:\n${resultStr}\n\n${prev}`);
  };

  // Helper function to handle API calls
  const handleApiCall = async (operation: string, apiCall: () => Promise<any>) => {
    setIsLoading(true);
    try {
      const result = await apiCall();
      logResult(operation, result);
    } catch (error) {
      logResult(`${operation} ERROR`, error);
    } finally {
      setIsLoading(false);
    }
  };

  // VM Management Functions
  const testVmStatus = () => handleApiCall('VM Status', async () => {
    console.log('Calling getVmStatus with UseIt-Dev-VM');
    return await window.App.getVmStatus('UseIt-Dev-VM');
  });
  const testStartVm = () => handleApiCall('Start VM', async () => {
    console.log('Calling startVm with UseIt-Dev-VM');
    return await window.App.startVm('UseIt-Dev-VM');
  });
  const testStopVm = () => handleApiCall('Stop VM', async () => {
    console.log('Calling stopVm with UseIt-Dev-VM');
    return await window.App.stopVm('UseIt-Dev-VM');
  });
  const testVmIp = () => handleApiCall('VM IP Address', async () => {
    console.log('Calling getVmIpAddress with UseIt-Dev-VM');
    return await window.App.getVmIpAddress('UseIt-Dev-VM');
  });
  const testCreateVm = () => handleApiCall('Create VM', async () => {
    console.log('Calling createVm');
    return await window.App.createVm();
  });
  const testVmDetails = () => handleApiCall('VM Details', async () => {
    console.log('Calling getVmDetails with UseIt-Dev-VM');
    // Note: getVmDetails expects a string parameter, not an object
    return await window.App.getVmDetails('UseIt-Dev-VM');
  });

  // System Functions
  const testEnableHyperV = () => handleApiCall('Enable Hyper-V', () => window.App.enableHyperV());
  const testRepairWindows = () => handleApiCall('Repair Windows Components', () => window.App.repairWindowsComponents());

  // Recording Functions
  const testRecorderApi = () => handleApiCall('Test Recorder API', async () => {
    console.log('Testing recorder API connection...');
    const result = await window.App.testRecorderApi();

    if (result.success) {
      logResult('RECORDER API SUCCESS', `✅ Recorder API is accessible`);
      logResult('API STATUS', `Status: ${JSON.stringify(result.status)}`);
    } else {
      logResult('RECORDER API FAILED', `❌ ${result.error}`);
      logResult('DETAILS', result.details || 'Check if recorder service is running on port 3000');
    }

    return result;
  });

  // RDP Functions
  const setupRdpConnections = async () => {
    try {
      logResult('RDP SETUP', 'Getting VM IP address...');

      // Get VM IP address using the SAME API call as the working button
      const vmIpResult = await window.App.getVmIpAddress('UseIt-Dev-VM');

      if (!vmIpResult) {
        throw new Error('Failed to get VM IP address. Make sure UseIt-Dev-VM is running.');
      }

      // The API returns the IP directly, not in an ipAddress property
      const vmIp = vmIpResult;
      logResult('VM IP OBTAINED', `VM IP: ${vmIp}`);

      // Check MSTSC service status
      const mstscStatus = await window.App.getMstscStatus();
      if (!mstscStatus || !mstscStatus.running) {
        throw new Error('MSTSC service is not running. Please start the service first.');
      }

      const mstscPort = mstscStatus.port || 9250;
      logResult('MSTSC STATUS', `MSTSC running on port ${mstscPort}`);

      // Generate RDP URLs with persistent session IDs
      const updatedConnections = await Promise.all(rdpConnections.map(async (conn, index) => {
        try {
          // Get fixed session ID (1-16) for this VM and session index
          const sessionIndex = index + 1; // 1, 2, 3, 4
          const sessionResult = await window.App.getPersistentRdpSession({
            vmName: conn.vmName,
            sessionIndex: sessionIndex,
            ip: vmIp,
            username: 'UseIt',
            password: '123456'
          });

          if (sessionResult.success) {
            const fixedSessionId = sessionResult.sessionId; // This will be "1", "2", "3", or "4"
            const rdpUrl = `http://localhost:${mstscPort}/auto-connect.html?ip=${vmIp}&username=UseIt&password=123456&sessionId=${fixedSessionId}`;
            logResult('FIXED SESSION', `${conn.name} -> Session ${fixedSessionId}`);

            return {
              ...conn,
              url: rdpUrl,
              status: 'connecting' as const,
              sessionId: fixedSessionId
            };
          } else {
            logResult('SESSION ERROR', `Failed to get fixed session for ${conn.name}: ${sessionResult.error}`);
            // Fallback to session index as string
            const fallbackSessionId = sessionIndex.toString();
            const rdpUrl = `http://localhost:${mstscPort}/auto-connect.html?ip=${vmIp}&username=UseIt&password=123456&sessionId=${fallbackSessionId}`;
            return {
              ...conn,
              url: rdpUrl,
              status: 'connecting' as const,
              sessionId: fallbackSessionId
            };
          }
        } catch (error: any) {
          logResult('SESSION ERROR', `Exception for ${conn.name}: ${error.message}`);
          return {
            ...conn,
            url: '',
            status: 'error' as const
          };
        }
      }));

      setRdpConnections(updatedConnections);
      logResult('RDP CONNECTIONS', `Generated ${updatedConnections.length} RDP connections with different session IDs`);

      return true;
    } catch (error: any) {
      logResult('RDP SETUP ERROR', error.message);
      return false;
    }
  };

  const openRdpGrid = async () => {
    setShowRdpGrid(true);
    setSelectedRdpView(null);

    // 直接自动获取IP并连接，不需要用户手动setup
    await setupRdpConnections();
    logResult('RDP GRID', 'Auto-setup and opened RDP Grid with real VM IP');
  };

  const selectRdpView = (id: number) => {
    setSelectedRdpView(id);
    const connection = rdpConnections.find(conn => conn.id === id);
    logResult('RDP VIEW SELECTED', `Selected ${connection?.name} - ${connection?.url}`);
  };

  const closeRdpGrid = () => {
    setShowRdpGrid(false);
    setSelectedRdpView(null);
    logResult('RDP GRID', 'Closed RDP Grid Overview');
  };

  const testRealRdpUrls = () => {
    // Update with more realistic test URLs
    setRdpConnections([
      { id: 1, name: 'Local VM 1', url: 'https://www.google.com', status: 'connecting', vmName: 'test-vm-1' },
      { id: 2, name: 'Local VM 2', url: 'https://www.github.com', status: 'connecting', vmName: 'test-vm-2' },
      { id: 3, name: 'Local VM 3', url: 'https://www.stackoverflow.com', status: 'connecting', vmName: 'test-vm-3' },
      { id: 4, name: 'Local VM 4', url: 'https://www.youtube.com', status: 'connecting', vmName: 'test-vm-4' }
    ]);
    logResult('RDP TEST', 'Updated RDP connections with test URLs for demonstration');
  };

  const updateRdpStatus = (id: number, status: 'connecting' | 'connected' | 'error') => {
    setRdpConnections(prev => prev.map(conn =>
      conn.id === id ? { ...conn, status } : conn
    ));
    logResult('RDP STATUS UPDATE', `${rdpConnections.find(c => c.id === id)?.name}: ${status}`);
  };

  // Effect to maintain iframe persistence
  useEffect(() => {
    // Log when RDP grid state changes
    if (showRdpGrid) {
      logResult('RDP GRID STATE', 'Grid is now visible - iframes will remain persistent');
    }
  }, [showRdpGrid, selectedRdpView]);

  const testStartRecording = () => handleApiCall('Start Recording', async () => {
    console.log('Starting video recording via API...');
    const result = await window.App.startRecording();

    if (result.success) {
      logResult('START RECORDING SUCCESS', '🎬 Recording started successfully via API!');
      logResult('API RESPONSE', `Data: ${JSON.stringify(result.data)}`);
    } else {
      logResult('START RECORDING FAILED', `❌ ${result.error}`);
      logResult('DETAILS', result.details || 'Check if recorder service is running');
    }

    return result;
  });

  const testStopRecording = () => handleApiCall('Stop Recording', async () => {
    console.log('Stopping video recording via API...');
    const result = await window.App.stopRecording();

    if (result.success) {
      logResult('STOP RECORDING SUCCESS', '⏹️ Recording stopped successfully via API!');
      logResult('API RESPONSE', `Data: ${JSON.stringify(result.data)}`);
    } else {
      logResult('STOP RECORDING FAILED', `❌ ${result.error}`);
      logResult('DETAILS', result.details || 'Check if recorder service is running');
    }

    return result;
  });

  const testRecordingStatus = () => handleApiCall('Recording Status', async () => {
    console.log('Checking recording status via API...');
    const result = await window.App.getRecordingStatus();

    if (result.success) {
      logResult('STATUS SUCCESS', '📊 Status retrieved successfully via API!');
      logResult('STATUS DATA', `Status: ${JSON.stringify(result.status)}`);
    } else {
      logResult('STATUS FAILED', `❌ ${result.error}`);
      logResult('DETAILS', result.details || 'Check if recorder service is running');
    }

    return result;
  });

  const testGetRecordedFilePath = () => handleApiCall('Get Recorded File Path', async () => {
    console.log('Getting recorded file path via API...');
    const result = await window.App.uploadRecording();

    if (result.success) {
      logResult('FILE PATH SUCCESS', '📁 File path retrieved successfully!');
      logResult('FILE INFO', `Name: ${result.videoFile?.name}`);
      logResult('FILE PATH', `Path: ${result.videoFile?.path}`);
      logResult('FILE SIZE', `Size: ${result.videoFile?.size} bytes`);
      logResult('CREATED AT', `Created: ${result.videoFile?.createdAt}`);
    } else {
      logResult('FILE PATH FAILED', `❌ ${result.error}`);
      logResult('DETAILS', result.details || 'Check if recording exists and service is running');
    }

    return result;
  });





  // RDP/MSTSC Functions
  const testMstscStatus = () => handleApiCall('MSTSC Status', () => window.App.getMstscStatus());
  const testMstscUrl = () => handleApiCall('MSTSC URL', () =>
    window.App.getMstscUrl({ ip: customIp, username: 'UseIt', password: '123456' })
  );
  const testCustomMstscUrl = () => handleApiCall(`MSTSC URL (Custom IP: ${customIp})`, () =>
    window.App.getMstscUrl({ ip: customIp, username: 'UseIt', password: '123456' })
  );

  // Diagnostic Functions
  const testDiagnoseVm = () => handleApiCall('Diagnose VM Environment', () => window.App.diagnoseVmEnvironment());

  // VM Client Functions
  const testVmClientUploadFile = () => handleApiCall('VM Client Upload File', async () => {
    // Convert text content to base64
    const fileData = btoa(vmClientFileContent);
    const filename = vmClientFilePath.split('\\').pop() || 'test_file.txt';

    return await window.App.vmClientUploadFile({
      ip: customIp,
      fileData: fileData,
      filePath: vmClientFilePath,
      filename: filename
    });
  });

  const testVmClientExecuteCommand = () => handleApiCall('VM Client Execute Command', async () => {
    return await window.App.vmClientExecuteCommand({
      ip: customIp,
      command: vmClientCommand,
      timeout: 30
    });
  });

  const testLocalVmAction = () => handleApiCall('Execute Local VM Action', async () => {
    const testAction = {
      type: 'click',
      coordinates: { x: 100, y: 100 },
      description: 'Test click action from RDP session'
    };
    return await window.App.executeLocalVmAction({
      action: testAction,
      runInstanceId: 'test-run-' + Date.now()
    });
  });

  const checkVmPort = () => handleApiCall('Check VM Port 7889', async () => {
    // Use the new electron API to check port 7889
    const result = await window.App.checkVmPort({
      vmName: 'UseIt-Dev-VM',
      port: 7889
    });

    if (result.success) {
      // Also check for Python processes if port is accessible
      const pythonCheck = await window.App.vmClientExecuteCommand({
        ip: result.vmIp,
        command: 'tasklist | findstr python',
        timeout: 10
      });

      return {
        ...result,
        pythonProcesses: pythonCheck
      };
    } else {
      return result;
    }
  });

  const checkRdpSessions = () => handleApiCall('Check RDP Sessions Status', async () => {
    const result = await window.App.getRdpSessionsStatus();
    return result;
  });



  // Advanced Test Functions
  const testFullVmWorkflow = async () => {
    setIsLoading(true);
    try {
      logResult('=== FULL VM WORKFLOW TEST ===', 'Starting comprehensive VM test...');

      // 1. Check VM Status
      const status = await window.App.getVmStatus('UseIt-Dev-VM');
      logResult('1. VM Status', status);

      // 2. Get VM IP if running
      if (status?.state === 'Running') {
        const ip = await window.App.getVmIpAddress('UseIt-Dev-VM');
        logResult('2. VM IP Address', ip);

        // 3. Test MSTSC Status
        const mstscStatus = await window.App.getMstscStatus();
        logResult('3. MSTSC Status', mstscStatus);

        // 4. Generate MSTSC URL
        if (ip) {
          const mstscUrl = await window.App.getMstscUrl({ ip, username: 'UseIt', password: '123456' });
          logResult('4. MSTSC URL', mstscUrl);
        }
      } else {
        logResult('2. VM Not Running', 'Skipping IP and MSTSC tests');
      }

      logResult('=== WORKFLOW COMPLETE ===', 'All tests finished');
    } catch (error) {
      logResult('WORKFLOW ERROR', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testFullRecordingWorkflow = async () => {
    setIsLoading(true);
    try {
      logResult('=== FULL RECORDING WORKFLOW TEST (HTTP API) ===', 'Starting comprehensive recording test...');

      // 1. Test recorder API
      const testResult = await window.App.testRecorderApi();
      logResult('1. Recorder API Test', testResult);

      if (testResult.success) {
        // 2. Check current recording status
        const currentStatus = await window.App.getRecordingStatus();
        logResult('2. Current Recording Status', currentStatus);

        // 3. Start recording
        const startResult = await window.App.startRecording();
        logResult('3. Start Recording', startResult);

        if (startResult.success) {
          // 4. Wait a moment and check status again
          setTimeout(async () => {
            try {
              const newStatus = await window.App.getRecordingStatus();
              logResult('4. Recording Status After Start', newStatus);

              // 5. Stop recording after a short test
              setTimeout(async () => {
                try {
                  const stopResult = await window.App.stopRecording();
                  logResult('5. Stop Recording', stopResult);

                  // 6. Get recorded file path for upload
                  if (stopResult.success) {
                    setTimeout(async () => {
                      try {
                        const filePathResult = await window.App.uploadRecording();
                        logResult('6. Get Recorded File Path', filePathResult);

                        if (filePathResult.success) {
                          logResult('WORKFLOW SUCCESS', '🎉 Complete recording workflow successful!');
                          logResult('READY FOR UPLOAD', `File ready: ${filePathResult.videoFile?.name}`);
                        }
                      } catch (error) {
                        logResult('6. File Path Error', error);
                      }
                    }, 2000);
                  }
                } catch (error) {
                  logResult('5. Stop Recording Error', error);
                }
              }, 5000); // Record for 5 seconds
            } catch (error) {
              logResult('4. Status Check Error', error);
            }
          }, 2000);
        }
      }

      logResult('=== RECORDING WORKFLOW COMPLETE ===', 'Recording test finished');
    } catch (error) {
      logResult('RECORDING WORKFLOW ERROR', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Quick test for known working URL
  const testKnownWorkingUrl = () => {
    const workingUrl = `http://localhost:9250/auto-connect.html?ip=${customIp}&username=UseIt&password=123456`;
    logResult('Known Working URL', workingUrl);
    // Try to open in new window for testing
    if (window.open) {
      window.open(workingUrl, '_blank', 'width=800,height=600');
      logResult('URL Test', 'Opened in new window for manual testing');
    }
  };

  // System info
  const showSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      windowSize: `${window.innerWidth}x${window.innerHeight}`,
      screenSize: `${window.screen.width}x${window.screen.height}`,
      timestamp: new Date().toISOString(),
      electronAPI: typeof window.App !== 'undefined',
      availableAPIs: window.App ? Object.keys(window.App) : []
    };
    logResult('System Information', info);
  };

  // API Availability Check
  const checkApiAvailability = () => {
    const apiChecks = {
      windowApp: typeof window.App !== 'undefined',
      vmApis: {
        getVmStatus: typeof window.App?.getVmStatus === 'function',
        startVm: typeof window.App?.startVm === 'function',
        stopVm: typeof window.App?.stopVm === 'function',
        getVmIpAddress: typeof window.App?.getVmIpAddress === 'function',
        createVm: typeof window.App?.createVm === 'function',
        getVmDetails: typeof window.App?.getVmDetails === 'function'
      },
      recordingApis: {
        testRecorderApi: typeof window.App?.testRecorderApi === 'function',
        startRecording: typeof window.App?.startRecording === 'function',
        stopRecording: typeof window.App?.stopRecording === 'function',
        getRecordingStatus: typeof window.App?.getRecordingStatus === 'function',
        uploadRecording: typeof window.App?.uploadRecording === 'function'
      },
      mstscApis: {
        getMstscStatus: typeof window.App?.getMstscStatus === 'function',
        getMstscUrl: typeof window.App?.getMstscUrl === 'function'
      }
    };
    logResult('API Availability Check', apiChecks);
  };

  // VM Diagnostic Test
  const runVmDiagnostic = async () => {
    setIsLoading(true);
    try {
      logResult('=== VM DIAGNOSTIC TEST ===', 'Starting comprehensive VM diagnostic...');

      // Test each VM API individually with detailed error handling
      const vmApis = [
        { name: 'getVmStatus', fn: () => window.App.getVmStatus('UseIt-Dev-VM') },
        { name: 'getVmDetails', fn: () => window.App.getVmDetails('UseIt-Dev-VM') },
        { name: 'getVmIpAddress', fn: () => window.App.getVmIpAddress('UseIt-Dev-VM') }
      ];

      for (const api of vmApis) {
        try {
          logResult(`Testing ${api.name}`, 'Calling API...');
          const result = await api.fn();
          logResult(`${api.name} SUCCESS`, result);
        } catch (error: any) {
          logResult(`${api.name} ERROR`, {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
        }
      }

      logResult('=== VM DIAGNOSTIC COMPLETE ===', 'All VM API tests finished');
    } catch (error) {
      logResult('DIAGNOSTIC ERROR', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear output
  const clearOutput = () => setOutput('');

  const titleBarHeight = 32;

  return (
    <div className="flex h-screen flex-col bg-background text-foreground">
      <TitleBar currentAppMode="debug" onModeChange={() => {}} />
      
      <main style={{ height: '100%', display: 'flex', flexDirection: 'column', background: 'var(--background)' }}>
        <div className="flex-grow flex w-full bg-background gap-0 h-full">

          {/* Left Panel - Controls */}
          <div className="w-1/3 bg-card text-card-foreground rounded-none shadow-lg p-4 overflow-y-scroll" style={{ maxHeight: 'calc(100vh - 64px)' }}>
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-bold text-foreground">Electron API Debug</h1>
              <Button onClick={() => navigate('/')} variant="outline" size="sm">
                Back to VM Setup
              </Button>
            </div>

            {/* Usage Instructions */}
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <h3 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">📋 HTTP API Recording Controls</h3>
              <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
                <li>• <strong>Test API:</strong> Check if recorder service is running on localhost:3000</li>
                <li>• <strong>Start Recording:</strong> POST /startRecording - Begin screen recording</li>
                <li>• <strong>Stop Recording:</strong> POST /stopRecording - Stop recording and save file</li>
                <li>• <strong>Get Status:</strong> GET /getStatus - Check current recording state</li>
                <li>• <strong>Get File Path:</strong> GET /getRecordedFilePath - Get recorded file path for upload</li>
                <li>• <strong>Full Workflow:</strong> Complete test sequence: API → Start → Status → Stop → File Path</li>
                <li>• <strong>Simplified:</strong> Direct API calls, no manual video.exe management needed</li>
              </ul>
            </div>

            {/* VM Management Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-orange-500">🖥️ VM Management</h2>
              <div className="grid grid-cols-2 gap-2">
                <Button onClick={testVmStatus} size="sm" disabled={isLoading}>VM Status</Button>
                <Button onClick={testStartVm} size="sm" disabled={isLoading}>Start VM</Button>
                <Button onClick={testStopVm} size="sm" disabled={isLoading}>Stop VM</Button>
                <Button onClick={testVmIp} size="sm" disabled={isLoading}>Get VM IP</Button>
                <Button onClick={testCreateVm} size="sm" disabled={isLoading}>Create VM</Button>
                <Button onClick={testVmDetails} size="sm" disabled={isLoading}>VM Details</Button>
              </div>
            </div>

            {/* System Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-blue-500">⚙️ System</h2>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={testEnableHyperV} size="sm" disabled={isLoading}>Enable Hyper-V</Button>
                <Button onClick={testRepairWindows} size="sm" disabled={isLoading}>Repair Windows</Button>
              </div>
            </div>

            {/* RDP Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-purple-500">🖥️ RDP Grid Overview</h2>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={openRdpGrid} size="sm" disabled={isLoading} variant="outline">
                  🔗 Open RDP Grid (Auto-get VM IP)
                </Button>
                <Button onClick={testRealRdpUrls} size="sm" disabled={isLoading} variant="outline">
                  🧪 Use Test URLs (for demo)
                </Button>
                {showRdpGrid && (
                  <Button onClick={closeRdpGrid} size="sm" variant="destructive">
                    ❌ Close RDP Grid
                  </Button>
                )}
              </div>
              {rdpConnections.some(c => c.url) && (
                <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded text-xs">
                  <div className="font-semibold text-green-800 dark:text-green-200">✅ RDP connections ready</div>
                  <div className="text-green-700 dark:text-green-300 mt-1">
                    VM IP: {(() => {
                      try {
                        return rdpConnections[0]?.url ? new URL(rdpConnections[0].url).searchParams.get('ip') : 'Unknown';
                      } catch {
                        return 'Unknown';
                      }
                    })()}
                  </div>
                  <div className="text-green-700 dark:text-green-300">
                    4 different RDP sessions configured (RDP Wrap)
                  </div>
                </div>
              )}
            </div>

            {/* Recording Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-red-500">🎥 Video Recording (HTTP API)</h2>
              <div className="grid grid-cols-3 gap-2">
                <Button onClick={testRecorderApi} size="sm" disabled={isLoading}>Test API</Button>
                <Button onClick={testStartRecording} size="sm" disabled={isLoading}>Start Recording</Button>
                <Button onClick={testStopRecording} size="sm" disabled={isLoading}>Stop Recording</Button>
                <Button onClick={testRecordingStatus} size="sm" disabled={isLoading}>Get Status</Button>
                <Button onClick={testGetRecordedFilePath} size="sm" disabled={isLoading}>Get File Path</Button>
                <Button onClick={testFullRecordingWorkflow} size="sm" disabled={isLoading} variant="outline">Full Workflow</Button>
              </div>
            </div>

            {/* RDP/MSTSC Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-green-500">🌐 RDP/MSTSC</h2>
              <div className="mb-3">
                <label className="block text-sm font-medium mb-1">Custom IP Address:</label>
                <input
                  type="text"
                  value={customIp}
                  onChange={(e) => setCustomIp(e.target.value)}
                  className="w-full px-3 py-1 text-sm border rounded bg-background text-foreground"
                  placeholder="Enter VM IP address"
                />
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={testMstscStatus} size="sm" disabled={isLoading}>MSTSC Status</Button>
                <Button onClick={testCustomMstscUrl} size="sm" disabled={isLoading}>
                  Generate MSTSC URL ({customIp})
                </Button>
                <Button onClick={testKnownWorkingUrl} size="sm" variant="outline" disabled={isLoading}>
                  🔗 Test Known Working URL
                </Button>
              </div>
            </div>

            {/* Diagnostic Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-purple-500">🔍 Diagnostics</h2>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={testDiagnoseVm} size="sm" disabled={isLoading}>Diagnose VM Env</Button>
              </div>
            </div>

            {/* VM Client Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-cyan-500">🤖 VM Client APIs</h2>
              <div className="mb-3 space-y-2">
                <div>
                  <label className="block text-xs font-medium mb-1">Command to Execute:</label>
                  <input
                    type="text"
                    value={vmClientCommand}
                    onChange={(e) => setVmClientCommand(e.target.value)}
                    className="w-full px-2 py-1 text-xs border rounded bg-background text-foreground"
                    placeholder="Enter command to execute"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1">File Path:</label>
                  <input
                    type="text"
                    value={vmClientFilePath}
                    onChange={(e) => setVmClientFilePath(e.target.value)}
                    className="w-full px-2 py-1 text-xs border rounded bg-background text-foreground"
                    placeholder="Target file path"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1">File Content:</label>
                  <textarea
                    value={vmClientFileContent}
                    onChange={(e) => setVmClientFileContent(e.target.value)}
                    className="w-full px-2 py-1 text-xs border rounded bg-background text-foreground"
                    placeholder="File content to upload"
                    rows={2}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={testVmClientExecuteCommand} size="sm" disabled={isLoading} variant="outline">
                  🚀 Execute Command
                </Button>
                <Button onClick={testVmClientUploadFile} size="sm" disabled={isLoading} variant="outline">
                  📁 Upload File
                </Button>
              </div>
              <div className="mt-2 p-2 bg-cyan-50 dark:bg-cyan-900/20 rounded text-xs">
                <div className="font-semibold text-cyan-800 dark:text-cyan-200">ℹ️ VM Client Info</div>
                <div className="text-cyan-700 dark:text-cyan-300 mt-1">
                  Target VM: {customIp}:3000
                </div>
                <div className="text-cyan-700 dark:text-cyan-300">
                  Uses vm_client recorder_win APIs
                </div>
              </div>
            </div>

            {/* Advanced Tests Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-yellow-500">🚀 Advanced Tests</h2>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={runVmDiagnostic} size="sm" disabled={isLoading} variant="secondary">
                  🔍 VM Diagnostic Test
                </Button>
                <Button onClick={testFullVmWorkflow} size="sm" disabled={isLoading} variant="secondary">
                  Full VM Workflow
                </Button>
                <Button onClick={testFullRecordingWorkflow} size="sm" disabled={isLoading} variant="secondary">
                  Full Recording Workflow
                </Button>
              </div>
            </div>



            {/* Control Section */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3 text-gray-500">🧹 Controls</h2>
              <div className="grid grid-cols-1 gap-2">
                <Button onClick={checkApiAvailability} variant="outline" size="sm">🔍 Check API Availability</Button>
                <Button onClick={showSystemInfo} variant="outline" size="sm">Show System Info</Button>
                <Button onClick={clearOutput} variant="outline" size="sm">Clear Output</Button>
              </div>
            </div>
          </div>

          {/* Right Panel - Output or RDP Grid */}
          <div className="w-2/3 bg-card text-card-foreground rounded-none shadow-lg p-4">
            {showRdpGrid ? (
              // RDP Grid View
              <div className="h-full">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-foreground">
                    {selectedRdpView ? `RDP View - ${rdpConnections.find(c => c.id === selectedRdpView)?.name}` : 'RDP Grid Overview (4 Local VMs)'}
                  </h2>
                  <div className="flex gap-2">
                    {selectedRdpView && (
                      <>
                        <Button onClick={() => setSelectedRdpView(null)} variant="outline" size="sm">
                          ← Back to Grid
                        </Button>
                        <Button onClick={testVmClientExecuteCommand} variant="outline" size="sm" disabled={isLoading}>
                          🚀 Execute Cmd
                        </Button>
                        <Button onClick={testVmClientUploadFile} variant="outline" size="sm" disabled={isLoading}>
                          📁 Upload File
                        </Button>
                      </>
                    )}
                    <Button onClick={closeRdpGrid} variant="destructive" size="sm">
                      ❌ Close
                    </Button>
                  </div>
                </div>

                {selectedRdpView ? (
                  // Single RDP View
                  <div className="h-full bg-gray-900 rounded-lg overflow-hidden relative" style={{ height: 'calc(100% - 80px)' }}>
                    <div className="absolute top-2 left-2 z-10 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                      {(() => {
                        const connection = rdpConnections.find(c => c.id === selectedRdpView);
                        return connection ? `${connection.name} - ${connection.status}` : 'Unknown';
                      })()}
                    </div>

                    {/* VM Client Quick Control Panel */}
                    <div className="absolute top-2 right-2 z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg max-w-xs">
                      <div className="text-xs font-semibold mb-2 text-cyan-300">🤖 VM Client Quick Controls</div>
                      <div className="space-y-2">
                        <div>
                          <input
                            type="text"
                            value={vmClientCommand}
                            onChange={(e) => setVmClientCommand(e.target.value)}
                            className="w-full px-2 py-1 text-xs bg-gray-800 text-white border border-gray-600 rounded"
                            placeholder="Command to execute"
                          />
                        </div>
                        <div className="flex gap-1">
                          <Button
                            onClick={testVmClientExecuteCommand}
                            size="sm"
                            disabled={isLoading}
                            className="text-xs px-2 py-1 h-auto"
                          >
                            🚀 Run
                          </Button>
                          <Button
                            onClick={testVmClientUploadFile}
                            size="sm"
                            disabled={isLoading}
                            className="text-xs px-2 py-1 h-auto"
                          >
                            📁 Upload
                          </Button>
                        </div>
                        <div className="flex gap-1 mt-1">
                          <Button
                            onClick={checkVmPort}
                            size="sm"
                            disabled={isLoading}
                            className="text-xs px-2 py-1 h-auto bg-blue-600 hover:bg-blue-700"
                          >
                            🔍 Check 7889
                          </Button>
                          <Button
                            onClick={checkRdpSessions}
                            size="sm"
                            disabled={isLoading}
                            className="text-xs px-2 py-1 h-auto bg-green-600 hover:bg-green-700"
                          >
                            📋 RDP Sessions
                          </Button>
                        </div>
                        <div className="flex gap-1 mt-1">
                          <Button
                            onClick={testLocalVmAction}
                            size="sm"
                            disabled={isLoading}
                            className="text-xs px-2 py-1 h-auto bg-purple-600 hover:bg-purple-700"
                          >
                            🎯 Test VM Action
                          </Button>
                        </div>
                        <div className="text-xs text-gray-400">
                          Target: {customIp}:3000
                        </div>
                      </div>
                    </div>
                    {(() => {
                      const connection = rdpConnections.find(c => c.id === selectedRdpView);
                      return connection ? (
                        <iframe
                          ref={(el) => {
                            if (el) iframeRefs.current[connection.id] = el;
                          }}
                          key={`rdp-${connection.id}`}
                          src={connection.url || 'about:blank'}
                          className="w-full h-full border-0"
                          onLoad={() => {
                            console.log(`[RDP] ${connection.name} iframe loaded`);
                            updateRdpStatus(connection.id, 'connected');
                          }}
                          onError={() => {
                            console.error(`[RDP] ${connection.name} iframe error`);
                            updateRdpStatus(connection.id, 'error');
                          }}
                          title={connection.name}
                          allow="clipboard-read; clipboard-write"
                          sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                        />
                      ) : null;
                    })()}
                  </div>
                ) : (
                  // Grid Overview
                  <div className="grid grid-cols-2 gap-4 h-full" style={{ height: 'calc(100% - 80px)' }}>
                    {rdpConnections.map((connection) => (
                      <div
                        key={connection.id}
                        className="bg-gray-900 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all"
                        onClick={() => selectRdpView(connection.id)}
                      >
                        <div className="p-3 bg-gray-800 flex items-center justify-between">
                          <div>
                            <h3 className="text-sm font-medium text-white">{connection.name}</h3>
                            <p className="text-xs text-gray-400">Session {connection.id}</p>
                          </div>
                          <div className={`w-2 h-2 rounded-full ${
                            connection.status === 'connected' ? 'bg-green-500' :
                            connection.status === 'connecting' ? 'bg-yellow-500 animate-pulse' :
                            'bg-red-500'
                          }`}></div>
                        </div>
                        <div className="h-48 relative">
                          <iframe
                            ref={(el) => {
                              if (el) gridIframeRefs.current[connection.id] = el;
                            }}
                            key={`grid-${connection.id}`}
                            src={connection.url || 'about:blank'}
                            className="w-full h-full border-0 pointer-events-none"
                            onLoad={() => {
                              console.log(`[RDP Grid] ${connection.name} thumbnail loaded`);
                              updateRdpStatus(connection.id, 'connected');
                            }}
                            onError={() => {
                              console.error(`[RDP Grid] ${connection.name} thumbnail failed to load`);
                              updateRdpStatus(connection.id, 'error');
                            }}
                            title={connection.name}
                            allow="clipboard-read; clipboard-write"
                            sandbox="allow-same-origin allow-scripts allow-forms"
                          />
                          <div className="absolute inset-0 bg-transparent hover:bg-black hover:bg-opacity-10 flex items-center justify-center">
                            <div className="opacity-0 hover:opacity-100 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm">
                              Click to expand
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // API Results View
              <div className="h-full">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-foreground">API Call Results</h2>
                  {isLoading && (
                    <div className="flex items-center text-orange-500">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500 mr-2"></div>
                      Loading...
                    </div>
                  )}
                </div>

                <div className="bg-black text-green-400 p-4 rounded-lg h-full overflow-y-auto font-mono text-sm">
                  {output || 'Click any button to test Electron APIs. Results will appear here...'}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
