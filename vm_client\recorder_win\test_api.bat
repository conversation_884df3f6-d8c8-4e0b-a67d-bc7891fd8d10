@echo off
echo ================================================
echo Recorder Show/Hide API Test
echo ================================================

echo.
echo Checking curl availability...
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: curl not found or not available
    echo Please install curl or use Windows 10+ built-in version
    pause
    exit /b 1
)
echo SUCCESS: curl is available

echo.
echo 1. Testing application status...
echo Command: curl -X GET http://localhost:3000/getStatus
curl -s -X GET http://localhost:3000/getStatus
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Application not running or API unavailable
    echo Please start the recorder application first and ensure it runs on port 3000
    pause
    exit /b 1
)
echo.
echo SUCCESS: Application is running

echo.
echo 2. Testing show window...
echo Command: curl -X POST http://localhost:3000/showWindow
curl -s -X POST http://localhost:3000/showWindow
echo.
echo SUCCESS: Show window command sent
echo Please observe if the recorder window appears from tray

echo.
echo Waiting 5 seconds to observe effect...
timeout /t 5 /nobreak >nul

echo.
echo 3. Testing hide window...
echo Command: curl -X POST http://localhost:3000/hideWindow
curl -s -X POST http://localhost:3000/hideWindow
echo.
echo SUCCESS: Hide window command sent
echo Please observe if the recorder window hides to tray

echo.
echo Waiting 5 seconds to observe effect...
timeout /t 5 /nobreak >nul

echo.
echo 4. Testing show window again...
echo Command: curl -X POST http://localhost:3000/showWindow
curl -s -X POST http://localhost:3000/showWindow
echo.
echo SUCCESS: Show window command sent again
echo Please observe if the recorder window appears again

echo.
echo ================================================
echo API Test Completed!
echo ================================================
echo.
echo Test Summary:
echo   - showWindow API: Shows recorder window from tray
echo   - hideWindow API: Hides recorder window to tray
echo   - Both APIs are called via HTTP POST
echo   - Application continues running in background when hidden
echo.
pause
