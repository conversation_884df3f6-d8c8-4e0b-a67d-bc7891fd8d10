# 持久化RDP Session管理

## 概述

为了解决RDP Wrap环境下每次连接都创建新session的问题，我们实现了持久化RDP session管理系统。这确保了每个VM的每个session都有固定的标识符，即使关闭tab后重新连接也会回到之前的session。

## 问题背景

在VM中运行RDP Wrap后，每次新的RDP连接都会创建新的session，而之前的session和其中运行的进程仍然存在但无法访问。这导致：

1. 资源浪费：多个无法访问的session占用系统资源
2. 进程丢失：之前session中运行的程序无法再次访问
3. 用户体验差：无法回到之前的工作环境

## 解决方案

### 1. 持久化Session管理

在主进程中实现了session管理系统：

```typescript
interface RdpSession {
  id: string;              // 持久化session ID
  vmName: string;          // VM名称
  sessionIndex: number;    // Session索引 (1, 2, 3, 4)
  ip: string;             // VM IP地址
  username: string;       // 用户名
  password: string;       // 密码
  createdAt: Date;        // 创建时间
  lastUsed: Date;         // 最后使用时间
  isActive: boolean;      // 是否活跃
}
```

### 2. Session ID生成规则

- **格式**: `persistent-{vmName}-{sessionIndex}-{timestamp}`
- **示例**: `persistent-UseIt-Dev-VM-1-1722434567890`
- **唯一性**: 每个VM的每个session index都有唯一的持久化ID

### 3. API接口

#### 获取持久化Session
```typescript
window.App.getPersistentRdpSession({
  vmName: 'UseIt-Dev-VM',
  sessionIndex: 1,
  ip: '*************',
  username: 'UseIt',
  password: '123456'
})
```

#### 查看Session状态
```typescript
window.App.getRdpSessionsStatus()
```

## 实现细节

### 1. 主进程 (main/index.ts)

- `persistentRdpSessions` Map存储所有session信息
- `getOrCreatePersistentSessionId()` 函数管理session创建和检索
- IPC handlers处理前端请求

### 2. Debug页面 (debug.tsx)

- RDP Grid使用持久化session ID
- 每个连接都有固定的session标识
- 新增"📋 RDP Sessions"按钮查看session状态

### 3. RDP View页面 (rdp-view-iframe.tsx)

- 支持URL参数传递sessionIndex和vmName
- 自动获取或创建持久化session
- 使用session ID生成mstsc连接URL

### 4. Frontend工具函数 (frontend/web/utils/electron.ts)

- `getPersistentRdpSession()` - 获取持久化session
- `getRdpSessionsStatus()` - 查看session状态

## 使用方法

### 1. Debug页面RDP Grid

1. 点击"🔗 Open RDP Grid"
2. 系统自动为每个VM slot分配持久化session
3. 每次打开都会连接到相同的session

### 2. 单独RDP连接

访问URL格式：
```
/rdp-mstsc?ip={VM_IP}&sessionIndex={1-4}&vmName={VM_NAME}
```

示例：
```
/rdp-mstsc?ip=*************&sessionIndex=1&vmName=UseIt-Dev-VM
```

### 3. 查看Session状态

在Debug页面的VM Client Quick Controls中点击"📋 RDP Sessions"按钮。

## Session映射

| Session Index | 用途 | 持久化ID示例 |
|--------------|------|-------------|
| 1 | Local VM 1 (主要session) | persistent-UseIt-Dev-VM-1-xxx |
| 2 | Local VM 2 | persistent-UseIt-Dev-VM-2-xxx |
| 3 | Local VM 3 | persistent-UseIt-Dev-VM-3-xxx |
| 4 | Local VM 4 | persistent-UseIt-Dev-VM-4-xxx |

## 技术优势

1. **Session持久化**: 每个session都有固定ID，重连时回到相同session
2. **资源管理**: 避免创建过多无用session
3. **进程保持**: 之前运行的程序可以继续访问
4. **用户体验**: 工作环境得以保持

## 注意事项

1. Session ID在应用重启后会重新生成，但session index保持一致
2. VM重启后所有session都会失效，需要重新创建
3. 建议定期清理不活跃的session以释放资源

## 测试验证

1. 打开Debug页面的RDP Grid
2. 连接到Local VM 1，运行一些程序
3. 关闭RDP tab
4. 重新打开Local VM 1，验证是否回到相同session
5. 检查之前运行的程序是否仍在运行
