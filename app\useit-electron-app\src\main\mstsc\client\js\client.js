/*
 * Copyright (c) 2015 Syl<PERSON><PERSON>te
 *
 * This file is part of mstsc.js.
 *
 * mstsc.js is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

(function() {
	/**
	 * Mouse button mapping
	 * @param button {integer} client button number
	 */
	function mouseButtonMap(button) {
		switch(button) {
		case 0:
			return 1;
		case 2:
			return 2;
		default:
			return 0;
		}
	};
	
	/**
	 * Mstsc client with performance optimizations
	 * Input client connection (mouse and keyboard)
	 * bitmap processing
	 * @param canvas {canvas} rendering element
	 */
	function Client(canvas) {
		this.canvas = canvas;
		// create renderer
		this.render = new Mstsc.Canvas.create(this.canvas); 
		this.socket = null;
		this.activeSession = false;
		
		// Performance optimization settings
		this.mouseThrottleMs = 16; // ~60fps mouse events
		this.lastMouseTime = 0;
		this.pendingMouseMove = null;
		
		// Keyboard event batching
		this.keyboardBatchMs = 8; // Batch keyboard events
		this.pendingKeyEvents = [];
		this.keyBatchTimer = null;
		
		this.install();
	}
	
	Client.prototype = {
		install : function () {
			var self = this;
			
			// Optimized mouse move event with throttling
			this.canvas.addEventListener('mousemove', function (e) {
				if (!self.socket) return;

				var now = performance.now();
				if (now - self.lastMouseTime < self.mouseThrottleMs) {
					// Store latest mouse position for delayed sending
					self.pendingMouseMove = {
						x: e.clientX,
						y: e.clientY,
						offset: Mstsc.elementOffset(self.canvas)
					};
					return;
				}

				self.lastMouseTime = now;
				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('mouse', x, y, 0, false);

				// Clear pending move since we just sent one
				self.pendingMouseMove = null;

				e.preventDefault || !self.activeSession();
				return false;
			});
			
			// Send any pending mouse move after throttle period
			setInterval(function() {
				if (self.pendingMouseMove && self.socket && self.activeSession) {
					var pending = self.pendingMouseMove;
					// Ensure coordinates are non-negative and within valid range
					var x = Math.max(0, Math.min(65535, Math.round(pending.x - pending.offset.left)));
					var y = Math.max(0, Math.min(65535, Math.round(pending.y - pending.offset.top)));
					self.socket.emit('mouse', x, y, 0, false);
					self.pendingMouseMove = null;
					self.lastMouseTime = performance.now();
				}
			}, self.mouseThrottleMs);
			
			this.canvas.addEventListener('mousedown', function (e) {
				if (!self.socket) return;

				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('mouse', x, y, mouseButtonMap(e.button), true);
				e.preventDefault();
				return false;
			});
			this.canvas.addEventListener('mouseup', function (e) {
				if (!self.socket || !self.activeSession) return;

				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('mouse', x, y, mouseButtonMap(e.button), false);
				e.preventDefault();
				return false;
			});
			this.canvas.addEventListener('contextmenu', function (e) {
				if (!self.socket || !self.activeSession) return;

				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('mouse', x, y, mouseButtonMap(e.button), false);
				e.preventDefault();
				return false;
			});
			this.canvas.addEventListener('DOMMouseScroll', function (e) {
				if (!self.socket || !self.activeSession) return;

				var isHorizontal = false;
				var delta = e.detail;
				var step = Math.max(1, Math.min(255, Math.round(Math.abs(delta) * 15 / 8)));

				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('wheel', x, y, step, delta > 0, isHorizontal);
				e.preventDefault();
				return false;
			});
			this.canvas.addEventListener('mousewheel', function (e) {
				if (!self.socket || !self.activeSession) return;

				var isHorizontal = Math.abs(e.deltaX) > Math.abs(e.deltaY);
				var delta = isHorizontal?e.deltaX:e.deltaY;
				var step = Math.max(1, Math.min(255, Math.round(Math.abs(delta) * 15 / 8)));

				var offset = Mstsc.elementOffset(self.canvas);
				// Ensure coordinates are non-negative and within valid range
				var x = Math.max(0, Math.min(65535, Math.round(e.clientX - offset.left)));
				var y = Math.max(0, Math.min(65535, Math.round(e.clientY - offset.top)));
				self.socket.emit('wheel', x, y, step, delta > 0, isHorizontal);
				e.preventDefault();
				return false;
			});
			
			// Enhanced keyboard events with focus management
			window.addEventListener('keydown', function (e) {
				if (!self.socket || !self.activeSession) return;

				console.log('[mstsc.js] 🎹 Key down:', e.key, e.code, 'scancode:', Mstsc.scancode(e));
				self.addKeyEvent(Mstsc.scancode(e), true);
				e.preventDefault();
				return false;
			});
			window.addEventListener('keyup', function (e) {
				if (!self.socket || !self.activeSession) return;

				console.log('[mstsc.js] 🎹 Key up:', e.key, e.code, 'scancode:', Mstsc.scancode(e));
				self.addKeyEvent(Mstsc.scancode(e), false);
				e.preventDefault();
				return false;
			});

			// 确保页面加载后自动聚焦以接收键盘事件
			window.addEventListener('load', function() {
				console.log('[mstsc.js] 🎯 Page loaded, focusing window for keyboard input');
				window.focus();
				if (self.canvas) {
					self.canvas.focus();
					// 设置canvas为可聚焦
					self.canvas.tabIndex = 0;
				}
			});

			// 点击canvas时聚焦
			this.canvas.addEventListener('click', function(e) {
				console.log('[mstsc.js] 🎯 Canvas clicked, focusing for keyboard input');
				self.canvas.focus();
				window.focus();
			});

			// 添加焦点事件监听
			window.addEventListener('focus', function() {
				console.log('[mstsc.js] 🎯 Window focused - keyboard input enabled');
			});

			window.addEventListener('blur', function() {
				console.log('[mstsc.js] 🎯 Window blurred - keyboard input may be disabled');
			});
			
			return this;
		},
		
		/**
		 * Add keyboard event to batch for efficient processing
		 */
		addKeyEvent: function(scancode, isPressed) {
			var self = this;
			
			// Add to pending events
			this.pendingKeyEvents.push({
				scancode: scancode,
				isPressed: isPressed,
				timestamp: performance.now()
			});
			
			// Schedule batch send if not already scheduled
			if (!this.keyBatchTimer) {
				this.keyBatchTimer = setTimeout(function() {
					self.flushKeyEvents();
				}, this.keyboardBatchMs);
			}
		},
		
		/**
		 * Flush all pending keyboard events
		 */
		flushKeyEvents: function() {
			if (this.pendingKeyEvents.length === 0) {
				this.keyBatchTimer = null;
				return;
			}
			
			// Send all pending key events
			for (var i = 0; i < this.pendingKeyEvents.length; i++) {
				var event = this.pendingKeyEvents[i];
				this.socket.emit('scancode', event.scancode, event.isPressed);
			}
			
			// Clear batch
			this.pendingKeyEvents = [];
			this.keyBatchTimer = null;
		},
		/**
		 * connect
		 * @param ip {string} ip target for rdp
		 * @param domain {string} microsoft domain
		 * @param username {string} session username
		 * @param password {string} session password
		 * @param sessionId {string} session identifier for persistence
		 * @param next {function} asynchrone end callback
		 */
		connect : function (ip, domain, username, password, sessionId, next) {
			// Handle both old (5 params) and new (6 params) function signatures
			if (typeof sessionId === 'function') {
				next = sessionId;
				sessionId = 'default';
			}

			// compute socket.io path (cozy cloud integration)
			var parts = document.location.pathname.split('/')
		      , base = parts.slice(0, parts.length - 1).join('/') + '/'
		      , path = base + 'socket.io';

			console.log('[mstsc.js] Connecting with sessionId:', sessionId);

			// start connection
			var self = this;
			this.socket = io(window.location.protocol + "//" + window.location.host, { "path": path }).on('rdp-connect', function() {
				// this event can be occured twice (RDP protocol stack artefact)
				console.log('[mstsc.js] connected to session:', sessionId);

				self.activeSession = true;
			}).on('rdp-bitmap', function(bitmap) {
				console.log('[mstsc.js] bitmap update bpp : ' + bitmap.bitsPerPixel);
				self.render.update(bitmap);
			}).on('rdp-close', function() {
				next(null);
				console.log('[mstsc.js] close session:', sessionId);
				self.activeSession = false;
			}).on('rdp-error', function (err) {
				next(err);
				console.log('[mstsc.js] error in session ' + sessionId + ': ' + err.code + '(' + err.message + ')');
				self.activeSession = false;
			});

			// emit infos event with sessionId
			this.socket.emit('infos', {
				ip : ip.indexOf(":")>-1 ? ip.split(":")[0] : ip,
				port : ip.indexOf(":")>-1 ? parseInt(ip.split(":")[1]) : 3389,
				screen : {
					width : this.canvas.width,
					height : this.canvas.height
				},
				domain : domain,
				username : username,
				password : password,
				sessionId : sessionId,
				locale : Mstsc.locale()
			});
		}
	}
	
	Mstsc.client = {
		create : function (canvas) {
			return new Client(canvas);
		}
	}
})();
