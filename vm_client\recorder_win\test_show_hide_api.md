# 测试 Show/Hide Window API

## 前提条件

1. 确保 recorder 应用正在运行
2. 应用监听在 `http://localhost:3000`
3. 安装了 curl 工具（Windows 10+ 自带）

## API 测试命令

### 1. 检查应用状态

```bash
curl -X GET http://localhost:3000/getStatus
```

**预期响应**:
```json
{
    "status": "success",
    "message": "Stopped"
}
```

### 2. 显示窗口

```bash
curl -X POST http://localhost:3000/showWindow
```

**预期响应**:
```json
{
    "status": "success",
    "message": "Window shown successfully"
}
```

**效果**: recorder 窗口会从托盘显示出来，出现在托盘图标附近

### 3. 隐藏窗口

```bash
curl -X POST http://localhost:3000/hideWindow
```

**预期响应**:
```json
{
    "status": "success", 
    "message": "Window hidden to tray successfully"
}
```

**效果**: recorder 窗口会隐藏到托盘，只在系统托盘显示图标

## 完整测试序列

### Windows 批处理测试 (test_api.bat)

```batch
@echo off
echo ================================================
echo 测试 Recorder Show/Hide API
echo ================================================

echo.
echo 1. 检查应用状态...
curl -s -X GET http://localhost:3000/getStatus
echo.

echo 2. 显示窗口...
curl -s -X POST http://localhost:3000/showWindow
echo.
echo 等待 3 秒观察效果...
timeout /t 3 /nobreak >nul

echo 3. 隐藏窗口...
curl -s -X POST http://localhost:3000/hideWindow
echo.
echo 等待 3 秒观察效果...
timeout /t 3 /nobreak >nul

echo 4. 再次显示窗口...
curl -s -X POST http://localhost:3000/showWindow
echo.

echo ================================================
echo 测试完成！
echo ================================================
pause
```

### Python 测试脚本 (test_api.py)

```python
import requests
import time

def test_show_hide_api():
    base_url = "http://localhost:3000"
    
    print("=== 测试 Show/Hide Window API ===\n")
    
    # 1. 检查状态
    print("1. 检查应用状态...")
    try:
        response = requests.get(f"{base_url}/getStatus")
        print(f"   状态: {response.json()}")
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    # 2. 显示窗口
    print("\n2. 显示窗口...")
    try:
        response = requests.post(f"{base_url}/showWindow")
        print(f"   响应: {response.json()}")
        print("   请观察窗口是否显示...")
        time.sleep(3)
    except Exception as e:
        print(f"   错误: {e}")
    
    # 3. 隐藏窗口
    print("\n3. 隐藏窗口...")
    try:
        response = requests.post(f"{base_url}/hideWindow")
        print(f"   响应: {response.json()}")
        print("   请观察窗口是否隐藏...")
        time.sleep(3)
    except Exception as e:
        print(f"   错误: {e}")
    
    # 4. 再次显示
    print("\n4. 再次显示窗口...")
    try:
        response = requests.post(f"{base_url}/showWindow")
        print(f"   响应: {response.json()}")
        print("   测试完成！")
    except Exception as e:
        print(f"   错误: {e}")

if __name__ == "__main__":
    test_show_hide_api()
```

## 错误处理

### 应用未运行
```bash
curl: (7) Failed to connect to localhost port 3000: Connection refused
```
**解决**: 启动 recorder 应用

### 应用未准备好
```json
{
    "status": "error",
    "message": "Application window is not ready"
}
```
**解决**: 等待应用完全启动

## 集成示例

### 在其他应用中调用

```javascript
// JavaScript 示例
async function showRecorder() {
    try {
        const response = await fetch('http://localhost:3000/showWindow', {
            method: 'POST'
        });
        const result = await response.json();
        console.log('显示结果:', result);
    } catch (error) {
        console.error('显示失败:', error);
    }
}

async function hideRecorder() {
    try {
        const response = await fetch('http://localhost:3000/hideWindow', {
            method: 'POST'
        });
        const result = await response.json();
        console.log('隐藏结果:', result);
    } catch (error) {
        console.error('隐藏失败:', error);
    }
}
```

```python
# Python 示例
import requests

def show_recorder():
    try:
        response = requests.post('http://localhost:3000/showWindow')
        return response.json()
    except Exception as e:
        return {'status': 'error', 'message': str(e)}

def hide_recorder():
    try:
        response = requests.post('http://localhost:3000/hideWindow')
        return response.json()
    except Exception as e:
        return {'status': 'error', 'message': str(e)}
```

## 注意事项

1. **端口**: 确保端口 3000 未被其他应用占用
2. **权限**: Windows 可能需要防火墙权限
3. **时序**: 显示/隐藏操作之间建议间隔 1-2 秒
4. **状态**: 窗口隐藏后应用仍在后台运行
5. **托盘**: 可以通过点击托盘图标手动显示/隐藏窗口
