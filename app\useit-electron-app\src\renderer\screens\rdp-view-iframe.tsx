import React, { useEffect, useState } from 'react';
import { Button } from 'renderer/components/ui/button';

interface RdpConfig {
  ip: string;
  username: string;
  password: string;
}

interface ServiceStatus {
  running: boolean;
  port: number;
  url: string;
}

export function RdpViewIframeScreen() {
  const [rdpConfig, setRdpConfig] = useState<RdpConfig | null>(null);
  const [mstscUrl, setMstscUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [sessionIndex, setSessionIndex] = useState<number>(1);
  const [vmName, setVmName] = useState<string>('UseIt-Dev-VM');

  // Check service status
  const checkServiceStatus = async (): Promise<boolean> => {
    try {
      const status = await window.App.getMstscStatus();
      console.log('[RDP View Iframe] Service status:', status);
      
      if (status.success) {
        setServiceStatus({
          running: status.running,
          port: status.port,
          url: status.url
        });
        return status.running;
      } else {
        console.error('[RDP View Iframe] Failed to get service status:', status.error);
        setError('无法检测MSTSC服务状态: ' + status.error);
        return false;
      }
    } catch (error: any) {
      console.error('[RDP View Iframe] Error checking service status:', error);
      setError('检测服务状态时出错: ' + error.message);
      return false;
    }
  };

  // Wait for service to be ready with retry mechanism
  const waitForService = async (maxRetries: number = 10): Promise<boolean> => {
    for (let i = 0; i < maxRetries; i++) {
      setRetryAttempts(i + 1);
      const isRunning = await checkServiceStatus();
      
      if (isRunning) {
        return true;
      }
      
      console.log(`[RDP View Iframe] Service not ready, retry ${i + 1}/${maxRetries}`);
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retry
      }
    }
    
    return false;
  };

  // Initialize RDP connection with persistent session
  const initializeConnection = async (ip: string, sessionIndex: number, vmName: string) => {
    try {
      // First check if service is running
      console.log('[RDP View Iframe] Checking service status...');
      const isServiceReady = await waitForService();

      if (!isServiceReady) {
        setError('MSTSC服务未能正常启动。请稍后重试或检查端口占用情况。');
        return;
      }

      console.log('[RDP View Iframe] Service is ready, getting credentials...');

      // Try to get secure credentials first
      const credentialsResult = await window.App.getRdpCredentials({ ip });
      console.log('[RDP View Iframe] getRdpCredentials result:', credentialsResult);

      const config = {
        ip,
        username: credentialsResult.success ? credentialsResult.credentials.username : 'UseIt',
        password: credentialsResult.success ? credentialsResult.credentials.password : '123456'
      };

      setRdpConfig(config);

      // Get or create persistent session
      console.log('[RDP View Iframe] Getting persistent session for:', vmName, 'session', sessionIndex);
      const sessionResult = await window.App.getPersistentRdpSession({
        vmName,
        sessionIndex,
        ip,
        username: config.username,
        password: config.password
      });

      console.log('[RDP View Iframe] Persistent session result:', sessionResult);

      if (sessionResult.success) {
        // Get mstsc URL with persistent session ID
        const urlResult = await window.App.getMstscUrl({
          ...config,
          sessionId: sessionResult.sessionId
        });
        console.log('[RDP View Iframe] getMstscUrl result with sessionId:', urlResult);

        if (urlResult.success) {
          setMstscUrl(urlResult.url);
          console.log('[RDP View Iframe] Using persistent session:', sessionResult.sessionId);
        } else {
          setError('获取MSTSC连接URL失败: ' + urlResult.error);
        }
      } else {
        setError('获取持久化session失败: ' + sessionResult.error);
      }

    } catch (error: any) {
      console.error('[RDP View Iframe] Error initializing connection:', error);
      setError('初始化RDP连接失败: ' + error.message);
    }
  };

  // Parse URL parameters and get credentials
  useEffect(() => {
    console.log('[RDP View Iframe] Screen mounted');
    console.log('Full URL:', window.location.href);

    // Try multiple ways to get IP and sessionIndex parameters
    let ip = null;
    let sessionIndex = null;
    let vmName = null;

    // Method 1: Standard URLSearchParams
    const urlParams = new URLSearchParams(window.location.search);
    ip = urlParams.get('ip');
    sessionIndex = urlParams.get('sessionIndex');
    vmName = urlParams.get('vmName');
    console.log('From URLSearchParams - IP:', ip, 'SessionIndex:', sessionIndex, 'VMName:', vmName);

    // Method 2: Check hash parameters (for electron-router-dom)
    if (!ip && window.location.hash) {
      const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
      ip = hashParams.get('ip');
      sessionIndex = sessionIndex || hashParams.get('sessionIndex');
      vmName = vmName || hashParams.get('vmName');
      console.log('From hash params - IP:', ip, 'SessionIndex:', sessionIndex, 'VMName:', vmName);
    }

    // Method 3: Parse manually from hash
    if (!ip && window.location.hash.includes('ip=')) {
      const ipMatch = window.location.hash.match(/ip=([^&]+)/);
      const sessionMatch = window.location.hash.match(/sessionIndex=([^&]+)/);
      const vmMatch = window.location.hash.match(/vmName=([^&]+)/);
      if (ipMatch) {
        ip = ipMatch[1];
        sessionIndex = sessionIndex || (sessionMatch ? sessionMatch[1] : null);
        vmName = vmName || (vmMatch ? vmMatch[1] : null);
        console.log('From manual parsing - IP:', ip, 'SessionIndex:', sessionIndex, 'VMName:', vmName);
      }
    }

    // Default values if not provided
    const finalSessionIndex = parseInt(sessionIndex || '1');
    const finalVmName = vmName || 'UseIt-Dev-VM';

    console.log('Final values - IP:', ip, 'SessionIndex:', finalSessionIndex, 'VMName:', finalVmName);

    if (ip) {
      // Save values to state for refresh functionality
      setSessionIndex(finalSessionIndex);
      setVmName(finalVmName);

      initializeConnection(ip, finalSessionIndex, finalVmName).finally(() => {
        setLoading(false);
      });
    } else {
      console.log('[RDP View Iframe] No IP found in URL parameters');
      setError('URL参数中未找到IP地址');
      setLoading(false);
    }
  }, []);

  const goBack = () => {
    window.history.back();
  };

  const openInNewWindow = () => {
    if (mstscUrl) {
      window.open(mstscUrl, '_blank', 'width=1024,height=768');
    }
  };

  // Test Local VM Action execution
  const testLocalVmAction = async () => {
    try {
      setTestResult('正在测试Local VM Action...');

      const testAction = {
        type: 'click',
        coordinates: { x: 100, y: 100 },
        description: 'Test click action from RDP session'
      };

      const result = await window.App.executeLocalVmAction({
        action: testAction,
        runInstanceId: 'test-run-' + Date.now()
      });

      if (result.success) {
        setTestResult(`✅ 测试成功! VM IP: ${result.vmIp}`);
      } else {
        setTestResult(`❌ 测试失败: ${result.error}`);
      }
    } catch (error: any) {
      setTestResult(`❌ 测试出错: ${error.message}`);
    }
  };

  const refreshConnection = () => {
    if (rdpConfig) {
      setLoading(true);
      setError(null);
      setMstscUrl(null);
      initializeConnection(rdpConfig.ip, sessionIndex, vmName).finally(() => {
        setLoading(false);
      });
    }
  };

  const forceRefreshIframe = () => {
    if (mstscUrl) {
      console.log('[RDP View Iframe] 🔄 Requesting screen refresh via iframe communication');

      const iframe = document.getElementById('mstsc-iframe') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        try {
          // Try to communicate with iframe to request refresh
          iframe.contentWindow.postMessage({
            type: 'refresh-screen',
            timestamp: Date.now()
          }, '*');
          console.log('[RDP View Iframe] 🔄 Refresh message sent to iframe');

          // Fallback: if iframe communication fails, reload iframe
          setTimeout(() => {
            if (iframe.src) {
              console.log('[RDP View Iframe] 🔄 Fallback: reloading iframe');
              iframe.src = iframe.src;
            }
          }, 2000);

        } catch (error) {
          console.log('[RDP View Iframe] ⚠️ Failed to communicate with iframe, reloading:', error);
          iframe.src = iframe.src;
        }
      }
    }
  };

  // 强制聚焦iframe以支持键盘输入
  const focusIframe = () => {
    const iframe = document.getElementById('mstsc-iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.focus();
      console.log('[RDP View Iframe] 🎯 Iframe manually focused for keyboard input');

      // 尝试聚焦iframe内部的内容
      try {
        if (iframe.contentWindow) {
          iframe.contentWindow.focus();
          console.log('[RDP View Iframe] 🎯 Iframe content window focused');
        }
      } catch (error) {
        console.log('[RDP View Iframe] ⚠️ Cannot focus iframe content window (cross-origin):', error);
      }
    }
  };

  // 键盘事件处理
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果焦点在iframe上，确保事件能正确传递
      const iframe = document.getElementById('mstsc-iframe') as HTMLIFrameElement;
      if (iframe && document.activeElement === iframe) {
        console.log('[RDP View Iframe] ⌨️ Key pressed while iframe focused:', e.key, e.code);
        // 不阻止事件，让它传递到iframe内部
      }
    };

    const handleClick = (e: MouseEvent) => {
      // 点击iframe区域时自动聚焦
      const iframe = document.getElementById('mstsc-iframe') as HTMLIFrameElement;
      if (iframe && e.target === iframe) {
        setTimeout(() => focusIframe(), 100);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClick);
    };
  }, [mstscUrl]);

  if (loading) {
    return (
      <div className="app-container window-frame h-screen flex flex-col bg-gray-900 text-white">
        <div className="bg-gray-800 p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={goBack} variant="outline" size="sm">
                ← 返回
              </Button>
              <h1 className="text-lg font-semibold">RDP连接 (mstsc.js)</h1>
            </div>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-blue-900 text-blue-100 p-6 rounded-lg max-w-md text-center">
            <p className="font-semibold mb-2">正在加载...</p>
            {retryAttempts > 0 && (
              <p className="text-sm mb-2">等待MSTSC服务启动... (尝试 {retryAttempts}/10)</p>
            )}
            <p className="text-sm">正在初始化mstsc.js连接...</p>
            {serviceStatus && (
              <div className="mt-4 text-xs text-blue-200">
                <p>服务状态: {serviceStatus.running ? '运行中' : '未运行'}</p>
                <p>端口: {serviceStatus.port}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app-container window-frame h-screen flex flex-col bg-gray-900 text-white">
        <div className="bg-gray-800 p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={goBack} variant="outline" size="sm">
                ← 返回
              </Button>
              <h1 className="text-lg font-semibold">RDP连接错误</h1>
            </div>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-red-900 text-red-100 p-6 rounded-lg max-w-md text-center">
            <p className="font-semibold mb-2">连接错误</p>
            <p className="text-sm mb-4">{error}</p>
            {serviceStatus && (
              <div className="mb-4 text-xs text-red-200">
                <p>服务状态: {serviceStatus.running ? '运行中' : '未运行'}</p>
                <p>端口: {serviceStatus.port}</p>
              </div>
            )}
            <div className="flex space-x-2 justify-center">
              <Button onClick={refreshConnection} variant="outline" size="sm">
                重试连接
              </Button>
              <Button onClick={goBack} variant="outline" size="sm">
                返回
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container window-frame h-screen flex flex-col bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      {/* Modern Header - Compact design */}
      <div className="bg-gray-900/80 backdrop-blur-sm px-3 py-2 border-b border-gray-700/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button onClick={goBack} variant="outline" size="sm" className="h-7 px-2 text-xs">
              ← 返回
            </Button>
            <h1 className="text-sm font-medium text-gray-200">
              RDP连接
            </h1>
            {rdpConfig && (
              <span className="text-xs text-gray-400">
                {rdpConfig.ip} - {rdpConfig.username}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-1.5">
            <Button onClick={focusIframe} variant="outline" size="sm" className="h-7 px-2 text-xs bg-orange-600 hover:bg-orange-700 text-white border-orange-600">
              ⌨️
            </Button>
            <Button onClick={forceRefreshIframe} variant="outline" size="sm" className="h-7 px-2 text-xs">
              🔄
            </Button>
            <Button onClick={refreshConnection} variant="outline" size="sm" className="h-7 px-2 text-xs">
              🔧
            </Button>
            <Button onClick={openInNewWindow} variant="outline" size="sm" className="h-7 px-2 text-xs">
              🗗
            </Button>
            <Button onClick={testLocalVmAction} variant="outline" size="sm" className="h-7 px-2 text-xs">
              🎯
            </Button>
            {serviceStatus && (
              <div className="text-xs text-gray-500">
                :{serviceStatus.port}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content - iframe */}
      <div className="flex-1 overflow-hidden">
        {mstscUrl && (
          <iframe
            id="mstsc-iframe"
            src={mstscUrl}
            className="w-full h-full border-none"
            title="mstsc.js RDP Connection"
            allow="fullscreen; keyboard-map *; clipboard-read; clipboard-write"
            sandbox="allow-same-origin allow-scripts allow-forms allow-pointer-lock allow-popups"
            tabIndex={0}
            onLoad={(e) => {
              console.log('[RDP View Iframe] Iframe loaded successfully');
              // 确保iframe获得焦点以支持键盘输入
              const iframe = e.target as HTMLIFrameElement;
              iframe.focus();
              // 延迟聚焦以确保iframe内容完全加载
              setTimeout(() => {
                iframe.focus();
                console.log('[RDP View Iframe] Iframe focused for keyboard input');
              }, 1000);
            }}
            onError={(e) => console.error('[RDP View Iframe] Iframe load error:', e)}
            onClick={(e) => {
              // 点击时确保iframe获得焦点
              const iframe = e.target as HTMLIFrameElement;
              iframe.focus();
              console.log('[RDP View Iframe] Iframe clicked and focused');
            }}
          />
        )}
      </div>

      {/* Compact Footer */}
      <div className="bg-gray-900/80 backdrop-blur-sm px-3 py-1 border-t border-gray-700/30">
        <div className="text-xs text-gray-500 text-center flex justify-between">
          <span>RDP视图</span>
          {testResult && (
            <span className="text-yellow-400">{testResult}</span>
          )}
          {mstscUrl && !testResult && (
            <span className="truncate max-w-xs">URL: {mstscUrl}</span>
          )}
        </div>
      </div>
    </div>
  );
} 