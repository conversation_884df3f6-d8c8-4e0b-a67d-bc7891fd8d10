{"name": "electron-screen-recorder", "version": "3.1.0", "description": "Screen Recorder", "main": "main.js", "author": "WX & YC", "scripts": {"start": "electron .", "clean": "rimraf dist release", "build": "electron-builder", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win --x64"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "depd": "^2.0.0", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.2", "tmp": "^0.2.1", "accepts": "^1.3.8", "array-flatten": "^1.1.1", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "cookie": "^0.6.0", "cookie-signature": "^1.0.6", "debug": "^2.6.9", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^1.2.0", "fresh": "^0.5.2", "http-errors": "^2.0.0", "merge-descriptors": "^1.0.1", "methods": "^1.1.2", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "path-to-regexp": "^0.1.7", "proxy-addr": "^2.0.7", "qs": "^6.11.0", "range-parser": "^1.2.1", "safe-buffer": "^5.2.1", "send": "^0.18.0", "serve-static": "^1.15.0", "setprototypeof": "^1.2.0", "statuses": "^2.0.1", "type-is": "^1.6.18", "utils-merge": "^1.0.1", "vary": "^1.1.2", "ms": "^2.1.3", "bytes": "^3.1.2", "unpipe": "^1.0.0", "destroy": "^1.2.0", "raw-body": "^2.5.2", "iconv-lite": "^0.4.24", "media-typer": "^0.3.0", "mime-types": "^2.1.35", "mime-db": "^1.52.0", "negotiator": "^0.6.3", "forwarded": "^0.2.0", "ipaddr.js": "^1.9.1", "ee-first": "^1.1.1", "mime": "^1.6.0", "side-channel": "^1.0.4", "call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "has-symbols": "^1.0.3", "object-inspect": "^1.12.3", "es-errors": "^1.3.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.0", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "rimraf": "^5.0.0"}, "build": {"appId": "com.example.electron-screen-recorder", "productName": "Screen Recorder", "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/video-recording.png", "certificateFile": null, "certificatePassword": null, "verifyUpdateCodeSignature": false}, "forceCodeSigning": false, "files": ["**/*", "!node_modules/ffmpeg-static${/*}", "!node_modules/ffprobe-static${/*}"], "extraResources": [{"from": "node_modules/ffmpeg-static/ffmpeg.exe", "to": "./ffmpeg.exe"}, {"from": "node_modules/ffprobe-static/bin/win32/x64/ffprobe.exe", "to": "./ffprobe.exe"}, {"from": "./InputListener.exe", "to": "./InputListener.exe"}, {"from": "./_internal", "to": "./_internal"}], "directories": {"output": "release"}, "asar": true, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}