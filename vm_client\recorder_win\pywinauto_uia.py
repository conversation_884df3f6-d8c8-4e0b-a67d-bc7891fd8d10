import time
import ctypes
import os
import datetime
from ctypes import windll, wintypes
from pywinauto import Desktop, Application
from pywinauto.findwindows import ElementNotFoundError

# 定义Windows API函数
GetCursorPos = windll.user32.GetCursorPos
GetCursorPos.argtypes = [ctypes.POINTER(wintypes.POINT)]
GetAsyncKeyState = windll.user32.GetAsyncKeyState
VK_LBUTTON = 0x01

# 创建日志目录
log_dir = "pywinauto_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 创建新的日志文件
def create_log_file():
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_path = os.path.join(log_dir, f"pywinauto_log_{timestamp}.txt")
    return open(log_path, "w", encoding="utf-8")

def get_clicked_element():
    """监测鼠标点击并获取点击的UI元素信息"""
    last_click_time = 0
    click_cooldown = 0.001  # 将冷却时间从0.03秒减少到0.001秒(1毫秒)，接近最小可行值
    last_key_state = 0
    was_pressed = False  # 跟踪上一帧鼠标是否按下
    ignored_clicks = 0
    processed_clicks = 0
    missed_elements = 0
    log_file = create_log_file()
    
    log_file.write("==== PyWinAuto点击检测日志 ====\n")
    log_file.write(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
    log_file.write(f"点击冷却时间: {click_cooldown}秒\n\n")
    log_file.flush()
    
    # 创建Desktop对象，用于获取屏幕上的元素
    desktop = Desktop(backend="uia")
    
    try:
        while True:
            try:
                current_time = time.time()
                # 获取当前鼠标位置
                pt = wintypes.POINT()
                GetCursorPos(ctypes.byref(pt))
                
                # 检测左键点击
                key_state = GetAsyncKeyState(VK_LBUTTON)
                is_pressed = (key_state & 0x8000) != 0
                key_state_changed = key_state != last_key_state
                
                # 记录键状态变化
                if key_state_changed:
                    log_file.write(f"时间: {datetime.datetime.now().strftime('%H:%M:%S.%f')} - "
                                  f"键状态变化: {last_key_state} -> {key_state} (pressed: {is_pressed})\n")
                    log_file.flush()
                
                # 只在鼠标从未按下变为按下的瞬间识别为点击
                if is_pressed and not was_pressed:
                    time_since_last = current_time - last_click_time
                    log_file.write(f"点击事件: 坐标({pt.x}, {pt.y}) 距上次: {time_since_last:.4f}秒\n")
                    
                    # 判断是否满足冷却时间
                    if time_since_last >= click_cooldown:
                        processed_clicks += 1
                        log_file.write(f"开始处理点击 [{processed_clicks}] (通过冷却检查)\n")
                        
                        # 获取点击位置
                        GetCursorPos(ctypes.byref(pt))
                        log_file.write(f"实际捕获位置: ({pt.x}, {pt.y})\n")
                        
                        # 最小延迟以确保元素状态稳定
                        time.sleep(0.005)
                        
                        # 获取点击位置的元素
                        start_element_time = time.time()
                        try:
                            # 尝试获取点击位置的元素
                            element = desktop.from_point(pt.x, pt.y)
                            element_time = time.time() - start_element_time
                            
                            log_file.write(f"元素获取成功 (耗时: {element_time:.4f}秒)\n")
                            
                            # 获取元素属性
                            element_name = element.window_text() if hasattr(element, 'window_text') else '未知'
                            element_class = element.class_name() if hasattr(element, 'class_name') else '未知'
                            element_automation_id = element.automation_id() if hasattr(element, 'automation_id') else '未知'
                            element_control_type = element.control_type() if hasattr(element, 'control_type') else '未知'
                            
                            log_file.write(f"  Name: {element_name}\n")
                            log_file.write(f"  Class: {element_class}\n")
                            log_file.write(f"  AutomationId: {element_automation_id}\n")
                            log_file.write(f"  ControlType: {element_control_type}\n")
                            
                            # 打印到控制台
                            print("\n=== 点击元素信息 ===")
                            print(f"位置: ({pt.x}, {pt.y})")
                            print(f"Name: {element_name}")
                            print(f"Class: {element_class}")
                            print(f"AutomationId: {element_automation_id}")
                            print(f"ControlType: {element_control_type}")
                            
                            # 尝试获取更多属性
                            if hasattr(element, 'get_properties'):
                                try:
                                    props = element.get_properties()
                                    log_file.write(f"  其他属性: {props}\n")
                                    print(f"其他属性: {props}")
                                except Exception as e:
                                    log_file.write(f"  属性获取错误: {str(e)}\n")
                        
                        except ElementNotFoundError:
                            missed_elements += 1
                            element_time = time.time() - start_element_time
                            log_file.write(f"元素获取失败 [累计: {missed_elements}] (耗时: {element_time:.4f}秒)\n")
                            print(f"\n=== 点击未检测到元素 ({missed_elements}) ===")
                            print(f"位置: ({pt.x}, {pt.y})")
                        except Exception as e:
                            missed_elements += 1
                            element_time = time.time() - start_element_time
                            error_msg = f"元素获取过程中发生错误: {str(e)}"
                            log_file.write(f"{error_msg} [累计: {missed_elements}] (耗时: {element_time:.4f}秒)\n")
                            print(f"\n=== 获取元素时出错 ({missed_elements}) ===")
                            print(f"位置: ({pt.x}, {pt.y})")
                            print(f"错误: {str(e)}")
                        
                        log_file.write(f"点击处理完成\n\n")
                        log_file.flush()
                        last_click_time = current_time
                    else:
                        ignored_clicks += 1
                        log_file.write(f"忽略点击 [{ignored_clicks}] (冷却中: {time_since_last:.4f}/{click_cooldown}秒)\n\n")
                        log_file.flush()
                
                # 更新鼠标状态
                was_pressed = is_pressed
                last_key_state = key_state
                time.sleep(0.002)  # 休眠时间，控制检测频率
                
            except KeyboardInterrupt:
                log_file.write("\n监听被用户中断\n")
                log_file.flush()
                print("\n监听已停止")
                break
            except Exception as e:
                error_msg = f"发生错误: {str(e)}"
                log_file.write(f"{error_msg}\n")
                log_file.flush()
                print(error_msg)
                continue
    finally:
        # 写入统计数据
        log_file.write("\n=== 统计信息 ===\n")
        log_file.write(f"已处理点击: {processed_clicks}\n")
        log_file.write(f"忽略的点击: {ignored_clicks}\n")
        log_file.write(f"未检测到元素: {missed_elements}\n")
        log_file.write(f"结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
        log_file.close()

def get_element_attributes(element):
    """获取元素的所有可能的属性信息"""
    attributes = {}
    
    # 尝试获取常用属性
    try_attributes = [
        'window_text', 'class_name', 'automation_id', 'control_type',
        'rectangle', 'is_visible', 'is_enabled', 'parent', 'children'
    ]
    
    for attr in try_attributes:
        if hasattr(element, attr):
            try:
                if callable(getattr(element, attr)):
                    attributes[attr] = getattr(element, attr)()
                else:
                    attributes[attr] = getattr(element, attr)
            except Exception:
                attributes[attr] = "获取失败"
    
    return attributes

if __name__ == "__main__":
    print(f"启动PyWinAuto监听器 (Ctrl+C 退出)...")
    print(f"日志保存在 '{os.path.abspath(log_dir)}' 目录下")
    print("请点击要检测的元素...")
    get_clicked_element() 