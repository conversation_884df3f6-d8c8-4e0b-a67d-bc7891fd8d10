# 新增API接口使用说明

本文档描述了新增的两个API接口：文件传输接口和命令执行接口。

## 服务信息

- **对外服务地址**: `http://localhost:3000` (主程序Electron应用)
- **内部服务地址**: `http://localhost:4000` (Python后端服务)
- **协议**: HTTP REST API
- **数据格式**: JSON

**注意**: 外部调用应使用端口3000，主程序会自动转发请求到内部的Python服务。

## 1. 文件传输接口

### 接口信息
- **URL**: `/uploadFile`
- **方法**: `POST`
- **功能**: 接收base64编码的文件数据并保存到指定路径

### 请求格式

```json
{
    "file_data": "base64编码的文件内容",
    "file_path": "目标文件路径或目录路径",
    "filename": "文件名"
}
```

### 请求参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| file_data | string | 是 | base64编码的文件内容 |
| file_path | string | 是 | 目标路径，可以是文件路径或目录路径 |
| filename | string | 是 | 文件名（会进行安全处理） |

### 响应格式

**成功响应**:
```json
{
    "status": "success",
    "message": "File uploaded successfully",
    "file_path": "实际保存的文件路径",
    "file_size": 文件大小字节数
}
```

**错误响应**:
```json
{
    "status": "error",
    "message": "错误描述"
}
```

### 使用示例

#### Python示例
```python
import requests
import base64

# 读取文件并编码
with open('local_file.txt', 'rb') as f:
    file_content = f.read()
    file_data_b64 = base64.b64encode(file_content).decode('utf-8')

# 上传文件
data = {
    "file_data": file_data_b64,
    "file_path": "C:\\Users\\<USER>\\Downloads\\uploaded_file.txt",
    "filename": "uploaded_file.txt"
}

response = requests.post(
    "http://localhost:3000/uploadFile",
    json=data,
    headers={'Content-Type': 'application/json'}
)

print(response.json())
```

#### JavaScript示例
```javascript
// 将文件转换为base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const base64 = reader.result.split(',')[1]; // 移除data:前缀
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

// 上传文件
async function uploadFile(file, targetPath) {
    const fileData = await fileToBase64(file);
    
    const response = await fetch('http://localhost:3000/uploadFile', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            file_data: fileData,
            file_path: targetPath,
            filename: file.name
        })
    });
    
    return response.json();
}
```

## 2. 命令执行接口

### 接口信息
- **URL**: `/executeCommand`
- **方法**: `POST`
- **功能**: 执行本机发送的cmd脚本命令

### 请求格式

```json
{
    "command": "要执行的命令",
    "working_dir": "工作目录(可选)",
    "timeout": 超时时间秒数(可选，默认30秒)
}
```

### 请求参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| command | string | 是 | 要执行的命令 |
| working_dir | string | 否 | 命令执行的工作目录 |
| timeout | number | 否 | 超时时间（秒），默认30秒 |

### 响应格式

**成功响应**:
```json
{
    "status": "success",
    "message": "Command executed successfully",
    "return_code": 命令返回码,
    "stdout": "标准输出内容",
    "stderr": "错误输出内容",
    "command": "执行的命令",
    "working_dir": "实际工作目录"
}
```

**错误响应**:
```json
{
    "status": "error",
    "message": "错误描述",
    "command": "尝试执行的命令"
}
```

### 使用示例

#### Python示例
```python
import requests

# 执行简单命令
data = {
    "command": "dir C:\\Users",
    "timeout": 10
}

response = requests.post(
    "http://localhost:3000/executeCommand",
    json=data,
    headers={'Content-Type': 'application/json'}
)

result = response.json()
if result['status'] == 'success':
    print(f"命令执行成功，返回码: {result['return_code']}")
    print(f"输出: {result['stdout']}")
else:
    print(f"命令执行失败: {result['message']}")
```

#### 带工作目录的示例
```python
# 在指定目录执行命令
data = {
    "command": "git status",
    "working_dir": "C:\\Users\\<USER>\\Documents\\GitHub\\vm_client",
    "timeout": 15
}

response = requests.post(
    "http://localhost:3000/executeCommand",
    json=data
)
```

#### JavaScript示例
```javascript
async function executeCommand(command, workingDir = null, timeout = 30) {
    const data = {
        command: command,
        timeout: timeout
    };
    
    if (workingDir) {
        data.working_dir = workingDir;
    }
    
    const response = await fetch('http://localhost:3000/executeCommand', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
    
    return response.json();
}

// 使用示例
executeCommand('echo Hello World')
    .then(result => {
        if (result.status === 'success') {
            console.log('输出:', result.stdout);
        } else {
            console.error('错误:', result.message);
        }
    });
```

## 安全注意事项

### 文件传输接口
1. 文件名会通过`secure_filename`进行安全处理
2. 自动创建不存在的目录
3. 支持相对路径和绝对路径
4. 建议限制上传文件大小和类型

### 命令执行接口
1. **高风险操作**：此接口可以执行任意系统命令
2. 建议在生产环境中添加命令白名单验证
3. 设置合理的超时时间避免长时间阻塞
4. 建议添加用户权限验证
5. 谨慎处理包含敏感信息的命令

## 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 408 | 请求超时（命令执行超时） |
| 500 | 服务器内部错误 |

## 测试

运行测试脚本验证接口功能：

```bash
python test_new_apis.py
```

测试脚本会验证：
- 文件上传功能
- 目录上传功能  
- 命令执行功能
- 带工作目录的命令执行功能

## 集成示例

### 完整的文件传输和命令执行流程

```python
import requests
import base64
import os

BASE_URL = "http://localhost:3000"

def upload_and_execute_script():
    # 1. 准备脚本文件
    script_content = """
@echo off
echo 开始执行脚本...
echo 当前时间: %date% %time%
dir
echo 脚本执行完成
"""
    
    # 2. 上传脚本文件
    script_b64 = base64.b64encode(script_content.encode('utf-8')).decode('utf-8')
    upload_data = {
        "file_data": script_b64,
        "file_path": "C:\\temp\\auto_script.bat",
        "filename": "auto_script.bat"
    }
    
    upload_response = requests.post(f"{BASE_URL}/uploadFile", json=upload_data)
    if upload_response.json()['status'] != 'success':
        print("脚本上传失败")
        return
    
    print("脚本上传成功")
    
    # 3. 执行上传的脚本
    execute_data = {
        "command": "C:\\temp\\auto_script.bat",
        "timeout": 30
    }
    
    execute_response = requests.post(f"{BASE_URL}/executeCommand", json=execute_data)
    result = execute_response.json()
    
    if result['status'] == 'success':
        print("脚本执行成功:")
        print(result['stdout'])
    else:
        print(f"脚本执行失败: {result['message']}")

# 运行示例
upload_and_execute_script()
```
