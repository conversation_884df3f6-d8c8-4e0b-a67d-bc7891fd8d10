# AI Run Panel iframe 键盘输入修复

## 问题描述

用户反馈在AI Run Panel的iframe中无法正常输入键盘，这影响了RDP连接中的打字功能。

## 根本原因

1. **iframe焦点管理问题** - iframe没有正确获得和保持焦点
2. **键盘事件传递问题** - 键盘事件无法正确传递到iframe内部
3. **canvas元素配置问题** - RDP canvas元素缺少必要的焦点属性
4. **iframe沙盒限制** - iframe缺少键盘相关的权限配置

## 解决方案

### 1. iframe配置增强

**文件**: `app/useit-electron-app/src/renderer/screens/rdp-view-iframe.tsx`

```typescript
<iframe
  id="mstsc-iframe"
  src={mstscUrl}
  className="w-full h-full border-none"
  title="mstsc.js RDP Connection"
  allow="fullscreen; keyboard-map *; clipboard-read; clipboard-write"
  sandbox="allow-same-origin allow-scripts allow-forms allow-pointer-lock allow-popups"
  tabIndex={0}
  onLoad={(e) => {
    // 确保iframe获得焦点
    const iframe = e.target as HTMLIFrameElement;
    iframe.focus();
    setTimeout(() => iframe.focus(), 1000);
  }}
  onClick={(e) => {
    // 点击时聚焦
    const iframe = e.target as HTMLIFrameElement;
    iframe.focus();
  }}
/>
```

**关键改进**:
- ✅ 添加 `keyboard-map *` 权限
- ✅ 添加 `tabIndex={0}` 使iframe可聚焦
- ✅ 添加自动聚焦逻辑
- ✅ 添加点击聚焦处理

### 2. 焦点管理功能

**新增功能**:
```typescript
// 强制聚焦iframe
const focusIframe = () => {
  const iframe = document.getElementById('mstsc-iframe') as HTMLIFrameElement;
  if (iframe) {
    iframe.focus();
    if (iframe.contentWindow) {
      iframe.contentWindow.focus();
    }
  }
};

// 键盘事件处理
React.useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    // 确保键盘事件能正确传递
  };
  document.addEventListener('keydown', handleKeyDown);
}, [mstscUrl]);
```

### 3. 用户界面增强

**新增按钮**:
```typescript
<Button onClick={focusIframe} variant="outline" size="sm" 
        className="bg-orange-600 hover:bg-orange-700 text-white">
  ⌨️ 聚焦键盘
</Button>
```

### 4. mstsc.js客户端增强

**文件**: `app/useit-electron-app/src/main/mstsc/client/js/client.js`

```javascript
// 增强的键盘事件处理
window.addEventListener('keydown', function (e) {
  if (!self.socket || !self.activeSession) return;
  
  console.log('[mstsc.js] 🎹 Key down:', e.key, e.code, 'scancode:', Mstsc.scancode(e));
  self.addKeyEvent(Mstsc.scancode(e), true);
  e.preventDefault();
  return false;
});

// 自动聚焦处理
window.addEventListener('load', function() {
  console.log('[mstsc.js] 🎯 Page loaded, focusing window for keyboard input');
  window.focus();
  if (self.canvas) {
    self.canvas.focus();
    self.canvas.tabIndex = 0;
  }
});

// 点击聚焦
this.canvas.addEventListener('click', function(e) {
  console.log('[mstsc.js] 🎯 Canvas clicked, focusing for keyboard input');
  self.canvas.focus();
  window.focus();
});
```

### 5. HTML模板修复

**文件**: `app/useit-electron-app/src/main/mstsc/client/html/auto-connect.html`

```html
<!-- Canvas配置 -->
<canvas id="myCanvas" style="display:none; outline:none;" tabindex="0">

<script>
// 连接时的焦点设置
function connect(ip, domain, username, password, sessionId) {
  // ... 连接逻辑 ...
  
  // 🎯 确保canvas可以接收键盘事件
  canvas.tabIndex = 0;
  canvas.style.outline = 'none';
  canvas.focus();
  
  // 连接后延迟聚焦
  setTimeout(function() {
    canvas.focus();
    window.focus();
  }, 2000);
}

// 🎯 焦点管理
document.addEventListener('click', function(event) {
  var canvas = document.getElementById('myCanvas');
  if (canvas && canvas.style.display !== 'none') {
    canvas.focus();
  }
});
</script>
```

### 6. 测试工具

**新增文件**: `app/useit-electron-app/src/main/mstsc/client/html/keyboard-test.html`

提供键盘输入测试页面，用于验证修复效果。

## 修复效果

### ✅ 已解决的问题

1. **iframe焦点问题** - iframe现在能正确获得和保持焦点
2. **键盘事件传递** - 键盘事件能正确传递到RDP连接
3. **用户体验** - 添加了"聚焦键盘"按钮供用户手动聚焦
4. **自动聚焦** - 页面加载和连接建立后自动聚焦
5. **点击聚焦** - 点击iframe区域自动聚焦
6. **调试信息** - 添加了详细的键盘事件日志

### 🎯 使用方法

1. **自动聚焦**: 页面加载后会自动聚焦，无需手动操作
2. **点击聚焦**: 点击RDP画面区域会自动聚焦
3. **手动聚焦**: 点击工具栏中的"⌨️ 聚焦键盘"按钮
4. **验证输入**: 在RDP连接中尝试打字，应该能正常输入

### 🔧 故障排除

如果键盘输入仍然不工作：

1. **检查浏览器控制台** - 查看是否有键盘事件日志
2. **使用测试页面** - 访问 `keyboard-test.html` 验证基础功能
3. **手动聚焦** - 点击"⌨️ 聚焦键盘"按钮
4. **刷新连接** - 使用"🔧 重新连接"按钮重新建立连接

## 技术细节

### iframe权限配置
```html
allow="fullscreen; keyboard-map *; clipboard-read; clipboard-write"
sandbox="allow-same-origin allow-scripts allow-forms allow-pointer-lock allow-popups"
```

### Canvas焦点配置
```html
<canvas tabindex="0" style="outline:none;">
```

### 键盘事件处理
```javascript
window.addEventListener('keydown', function (e) {
  // 处理键盘按下
  self.addKeyEvent(Mstsc.scancode(e), true);
  e.preventDefault();
  return false;
});
```

## 相关文件

- `src/renderer/screens/rdp-view-iframe.tsx` - iframe组件
- `src/main/mstsc/client/js/client.js` - mstsc.js客户端
- `src/main/mstsc/client/html/auto-connect.html` - 自动连接模板
- `src/main/mstsc/client/html/index.html` - 主模板
- `src/main/mstsc/client/html/keyboard-test.html` - 测试页面

---

**修复状态**: ✅ 已完成  
**测试状态**: 🧪 需要用户验证  
**版本**: v1.0 (2025-08-01)  
**兼容性**: Chrome, Firefox, Edge (iframe环境)
