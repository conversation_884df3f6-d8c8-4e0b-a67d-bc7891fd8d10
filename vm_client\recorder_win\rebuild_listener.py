#!/usr/bin/env python3
"""
重新编译listener.py为InputListener.exe的脚本
"""

import os
import sys
import subprocess
import shutil

def main():
    print("=" * 50)
    print("重新编译 InputListener.exe")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('listener.py'):
        print("错误: 找不到 listener.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    print("\n1. 检查依赖...")
    
    # 检查PyInstaller
    try:
        result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ PyInstaller 版本: {result.stdout.strip()}")
        else:
            print("❌ PyInstaller 未安装，正在安装...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller 安装完成")
    except Exception as e:
        print(f"❌ PyInstaller 检查失败: {e}")
        return False
    
    # 检查其他依赖
    required_packages = ['flask', 'requests', 'werkzeug', 'pynput', 'psutil', 'cryptography', 'waitress']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing_packages, check=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    print("\n2. 清理旧文件...")
    
    # 清理旧的编译文件
    cleanup_dirs = ['build', 'dist']
    cleanup_files = ['InputListener.exe']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 删除目录: {dir_name}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 删除文件: {file_name}")
    
    print("\n3. 开始编译...")
    
    # 使用PyInstaller编译
    try:
        if os.path.exists('InputListener.spec'):
            print("使用现有的 InputListener.spec 配置文件")
            cmd = [sys.executable, '-m', 'PyInstaller', 'InputListener.spec']
        else:
            print("使用默认配置编译")
            cmd = [sys.executable, '-m', 'PyInstaller', 
                   '--onedir',  # 使用目录模式而不是单文件
                   '--console',
                   '--name=InputListener',
                   'server.py']
        
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 编译成功")
        else:
            print("❌ 编译失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 编译超时")
        return False
    except Exception as e:
        print(f"❌ 编译过程出错: {e}")
        return False
    
    print("\n4. 复制文件...")
    
    # 复制编译好的文件
    exe_source = os.path.join('dist', 'InputListener', 'InputListener.exe')
    internal_source = os.path.join('dist', 'InputListener', '_internal')
    
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, 'InputListener.exe')
        print("✅ 复制 InputListener.exe")
    else:
        print("❌ 找不到编译后的 InputListener.exe")
        return False
    
    if os.path.exists(internal_source):
        if os.path.exists('_internal'):
            shutil.rmtree('_internal')
        shutil.copytree(internal_source, '_internal')
        print("✅ 复制 _internal 目录")
    else:
        print("⚠️  找不到 _internal 目录")
    
    print("\n5. 验证编译结果...")
    
    if os.path.exists('InputListener.exe'):
        file_size = os.path.getsize('InputListener.exe')
        print(f"✅ InputListener.exe 大小: {file_size:,} 字节")
        
        # 简单测试exe是否能运行
        try:
            result = subprocess.run(['InputListener.exe', '--help'], 
                                  capture_output=True, text=True, timeout=5)
            print("✅ 可执行文件测试通过")
        except:
            print("⚠️  可执行文件测试失败，但文件已创建")
    else:
        print("❌ InputListener.exe 未创建")
        return False
    
    print("\n" + "=" * 50)
    print("编译完成！")
    print("=" * 50)
    print("\n文件列表:")
    print("- InputListener.exe (主程序)")
    print("- _internal/ (依赖库)")
    print("\n现在可以测试新的API接口:")
    print("1. 启动应用程序")
    print("2. 测试命令: curl -X POST http://localhost:3000/executeCommand -H \"Content-Type: application/json\" -d \"{\\\"command\\\": \\\"echo Hello World\\\"}\"")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n编译失败，请检查错误信息")
        sys.exit(1)
