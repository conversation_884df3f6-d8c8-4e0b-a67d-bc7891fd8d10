import { app, ipcMain, BrowserWindow } from 'electron'
import { exec, spawn } from 'child_process'
import path from 'path'
import fs from 'fs'
import sudo from 'sudo-prompt'
import rdp from 'node-rdpjs-2'

import { makeAppWithSingleInstanceLock } from 'lib/electron-app/factories/app/instance'
import { startMstscService, stopMstscService, getMstscAutoConnectUrl, getMstscServiceStatus } from './mstsc-service'
import { makeAppSetup } from 'lib/electron-app/factories/app/setup'
import { MainWindow } from './windows/main'
import { RdpPopupWindow } from './windows/rdp-popup'
import { RdpServer } from './rdp-server'
import { RdpTest } from './rdp-test'

import type { IpcMainInvokeEvent } from 'electron'

// 清理现有的IPC处理器，防止热重载时重复注册
ipcMain.removeAllListeners('get-mstsc-url')
ipcMain.removeAllListeners('get-mstsc-status')
ipcMain.removeAllListeners('enable-hyper-v')
ipcMain.removeAllListeners('create-vm')
ipcMain.removeAllListeners('start-vm')
ipcMain.removeAllListeners('stop-vm')
ipcMain.removeAllListeners('delete-vm')
ipcMain.removeAllListeners('get-vm-status')
ipcMain.removeAllListeners('get-vm-ip')
ipcMain.removeAllListeners('list-vms')
ipcMain.removeAllListeners('show-rdp-popup')
ipcMain.removeAllListeners('start-recording')
ipcMain.removeAllListeners('stop-recording')
ipcMain.removeAllListeners('get-recording-status')
ipcMain.removeAllListeners('upload-recording')
ipcMain.removeAllListeners('test-recorder-api')

ipcMain.removeAllListeners('diagnose-vm-environment')
ipcMain.removeAllListeners('getVmDetails')
ipcMain.removeAllListeners('trigger-rdp-automation')
ipcMain.removeAllListeners('debug-transfer-files')
ipcMain.removeAllListeners('debug-execute-commands')
ipcMain.removeAllListeners('execute-local-vm-action')
ipcMain.removeAllListeners('check-vm-port')
ipcMain.removeAllListeners('get-persistent-rdp-session')
ipcMain.removeAllListeners('get-rdp-sessions-status')
ipcMain.removeAllListeners('ai-run-relay-start')
ipcMain.removeAllListeners('ai-run-relay-message')
ipcMain.removeAllListeners('ai-run-relay-stop')
ipcMain.removeAllListeners('ai-run-relay-pause')
ipcMain.removeAllListeners('ai-run-relay-resume')

// Initialize both RDP servers
const rdpServer = new RdpServer()

// VM management functions for internal use
const startVm = async (vmName: string) => {
  const scriptName = 'start-vm.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);
  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName "${vmName}"`;

  return new Promise((resolve, reject) => {
    const options = { name: 'UseIt Start VM' };
    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer) => {
      if (error) {
        console.error('Start VM Error:', error);
        reject(error);
        return;
      }
      const output = stdout?.toString().trim() || '';
      const success = output.includes('SUCCESS') || output.includes('already running');
      resolve({ success, message: output });
    });
  });
}

const stopVm = async (vmName: string) => {
  const scriptName = 'stop-vm.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);
  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName "${vmName}"`;

  return new Promise((resolve, reject) => {
    const options = { name: 'UseIt Stop VM' };
    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer) => {
      if (error) {
        console.error('Stop VM Error:', error);
        reject(error);
        return;
      }
      const output = stdout?.toString().trim() || '';
      const success = output.includes('SUCCESS') || output.includes('already off');
      resolve({ success, message: output });
    });
  });
}

makeAppWithSingleInstanceLock(async () => {
  await app.whenReady()

  // Start legacy RDP server
  try {
    await rdpServer.start()
  } catch (error) {
    console.error('Failed to start legacy RDP server:', error)
  }

  // Start mstsc.js service
  try {
    await startMstscService()
    console.log('mstsc.js service started successfully')
  } catch (error) {
    console.error('Failed to start mstsc.js service:', error)
  }

  // Auto-start VM on application startup
  try {
    console.log('Auto-starting VM on application startup...')
    const vmResult = await startVm('UseIt-Dev-VM')
    console.log('VM auto-start result:', vmResult)

    console.log('VM started successfully.')

  } catch (error) {
    console.error('Failed to auto-start VM:', error)
  }

  // Initialize fixed RDP sessions (1-16)
  await initializeFixedRdpSessions()

  await makeAppSetup(MainWindow)
})

// 清理资源
app.on('before-quit', async () => {
  console.log('Cleaning up resources...')

  // Stop VM before quitting
  try {
    console.log('Auto-stopping VM on application quit...')
    const vmResult = await stopVm('UseIt-Dev-VM')
    console.log('VM auto-stop result:', vmResult)
  } catch (error) {
    console.error('Failed to auto-stop VM:', error)
  }

  // Stop RDP services
  stopMstscService()
})

// 获取mstsc自动连接URL
ipcMain.handle('get-mstsc-url', (event, { ip, username = 'UseIt', password = '123456', domain = '', sessionId }) => {
  try {
    const url = getMstscAutoConnectUrl(ip, username, password, domain, sessionId)
    console.log('[IPC] Generated mstsc URL:', url)
    return { success: true, url }
  } catch (error: any) {
    console.error('[IPC] Error generating mstsc URL:', error)
    return { success: false, error: error.message }
  }
})

// 获取mstsc服务状态
ipcMain.handle('get-mstsc-status', () => {
  try {
    const status = getMstscServiceStatus()
    console.log('[IPC] MSTSC service status:', status)
    return { success: true, ...status }
  } catch (error: any) {
    console.error('[IPC] Error getting mstsc status:', error)
    return { success: false, error: error.message, running: false }
  }
})







// Debug: 获取活跃的RDP客户端列表
ipcMain.handle('get-active-rdp-clients', () => {
  try {
    const clients = Array.from(activeRdpClients.keys());
    console.log('Active RDP clients:', clients);
    return {
      success: true,
      clients,
      count: clients.length
    };
  } catch (error: any) {
    console.error('Failed to get active RDP clients:', error);
    return {
      success: false,
      error: error.message,
      clients: [],
      count: 0
    };
  }
})

ipcMain.handle('enable-hyper-v', () => {
  const scriptName = 'enable-hyper-v.bat';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt Electron App'
    };

    // For release, you should revert to this line for a silent, background execution:
    // sudo.exec(`"${scriptPath}"`, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
    
    // For debugging, we'll open the script in a new admin command prompt to see the output.
    const command = `start cmd /k "${scriptPath}"`;

    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        // This error usually means the user cancelled the UAC prompt.
        reject(error);
        return;
      }
      // Since the script runs in a new window, stdout/stderr here will likely be empty.
      // We resolve immediately to let the UI know the process has started.
      resolve('Administrator command prompt opened. See the new window for progress.');
    });
  });
})

ipcMain.handle('repair-windows-components', () => {
  const scriptName = 'repair-windows-components.bat';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt Windows Repair'
    };

    // For debugging, we'll open the script in a new admin command prompt to see the output.
    const command = `start cmd /k "${scriptPath}"`;

    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        // This error usually means the user cancelled the UAC prompt.
        reject(error);
        return;
      }
      // Since the script runs in a new window, stdout/stderr here will likely be empty.
      // We resolve immediately to let the UI know the process has started.
      resolve('Windows Component Repair script opened. See the new window for progress.');
    });
  });
})

ipcMain.handle('create-vm', async () => {
  const scriptName = 'create-vm.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}"`;

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt VM Creator'
    };
    // For debugging, we'll open the script in a new admin command prompt to see the output.
    // Note: We use `cmd /k` to launch powershell to ensure the window stays open after the script finishes.
    const debugCommand = `start cmd /k ${command}`;

    sudo.exec(debugCommand, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        reject(error);
        return;
      }
      // Since the script runs in a new window, we resolve immediately.
      resolve('Administrator PowerShell prompt opened. See the new window for progress.');
    });
  });
})

ipcMain.handle('get-vm-status', async (event: IpcMainInvokeEvent, vmName: string) => {
  return new Promise((resolve, reject) => {
    console.log(`[VM Status] Checking status for VM: ${vmName}`);

    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.error('[VM Status] Operation timed out after 20 seconds');
      resolve({
        state: 'Timeout',
        rawOutput: 'Operation timed out',
        error: 'VM status check timed out after 20 seconds'
      });
    }, 20000);

    // Use a simpler, more reliable command first
    const simpleCommand = `powershell -Command "try { Get-VM -Name '${vmName}' | Select-Object -ExpandProperty State } catch { 'NotFound' }"`;

    console.log(`[VM Status] Executing simple command: ${simpleCommand}`);

    exec(simpleCommand, { timeout: 15000 }, (error, stdout, stderr) => {
      clearTimeout(timeoutId);

      console.log(`[VM Status] Simple command completed`);
      console.log(`[VM Status] Error:`, error);
      console.log(`[VM Status] Stdout:`, stdout);
      console.log(`[VM Status] Stderr:`, stderr);

      if (error) {
        console.warn('[VM Status] Simple command failed, trying with elevated privileges...');

        // Try with elevated privileges as fallback
        const options = {
          name: 'UseIt VM Status'
        };

        sudo.exec(simpleCommand, options, (sudoError?: Error, sudoStdout?: string | Buffer, sudoStderr?: string | Buffer) => {
          console.log(`[VM Status] Sudo command completed`);
          console.log(`[VM Status] Sudo Error:`, sudoError);
          console.log(`[VM Status] Sudo Stdout:`, sudoStdout);
          console.log(`[VM Status] Sudo Stderr:`, sudoStderr);

          if (sudoError) {
            console.error('[VM Status] Both regular and elevated commands failed');
            resolve({
              state: 'Error',
              rawOutput: stderr ? stderr.toString() : error.message,
              error: `Failed to get VM status: ${error.message}`,
              method: 'failed'
            });
            return;
          }

          const output = sudoStdout?.toString().trim() || '';
          console.log(`[VM Status] Sudo result: ${output}`);

          resolve({
            state: output || 'Unknown',
            rawOutput: output,
            method: 'elevated'
          });
        });
        return;
      }

      const output = stdout?.toString().trim() || '';
      console.log(`[VM Status] Simple command result: ${output}`);

      resolve({
        state: output || 'Unknown',
        rawOutput: output,
        method: 'simple'
      });
    });
  });
});

ipcMain.handle('start-vm', async (event: IpcMainInvokeEvent, vmName: string) => {
  const scriptName = 'start-vm.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName "${vmName}"`;

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt Start VM'
    };

    // Execute the command and capture output
    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        console.error('Start VM Error:', error);
        reject(error);
        return;
      }

      const output = stdout?.toString().trim() || '';
      console.log('Start VM Output:', output);

      // Check if the operation was successful
      const success = output.includes('SUCCESS') || output.includes('already running');

      resolve({
        success,
        message: output,
        rawOutput: output
      });
    });
  });
});

ipcMain.handle('stop-vm', async (event: IpcMainInvokeEvent, vmName: string) => {
  const scriptName = 'stop-vm.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName "${vmName}"`;

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt Stop VM'
    };

    // Execute the command and capture output
    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        console.error('Stop VM Error:', error);
        reject(error);
        return;
      }

      const output = stdout?.toString().trim() || '';
      console.log('Stop VM Output:', output);

      // Check if the operation was successful
      const success = output.includes('SUCCESS') || output.includes('already off');

      resolve({
        success,
        message: output,
        rawOutput: output
      });
    });
  });
});

ipcMain.handle('get-vm-ip', async (event: IpcMainInvokeEvent, vmName: string) => {
  const scriptName = 'get-vm-ip.ps1';
  const scriptDir = app.isPackaged
    ? path.join(process.resourcesPath, 'scripts')
    : path.join(app.getAppPath(), 'src/resources/scripts');

  const scriptPath = path.join(scriptDir, scriptName);

  const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName ${vmName}`;

  return new Promise((resolve, reject) => {
    const options = {
      name: 'UseIt Get VM IP'
    };
    
    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      if (error) {
        reject(error);
        return;
      }
      if (stderr) {
        reject(new Error(stderr.toString()));
        return;
      }
      const output = stdout ? stdout.toString() : '';
      const successMatch = output.match(/SUCCESS: Found IP address: (.*)/);
      if (successMatch && successMatch[1]) {
        resolve(successMatch[1].trim());
      } else {
        reject(new Error(output.trim() || 'Failed to get VM IP address.'));
      }
    });
  });
});



// Get detailed VM information
ipcMain.handle('getVmDetails', async (event: IpcMainInvokeEvent, { vmName }: { vmName: string }) => {
  return new Promise((resolve, reject) => {
    console.log(`[VM Details] Getting details for VM: ${vmName}`);

    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.error('[VM Details] Operation timed out after 15 seconds');
      resolve({
        success: false,
        error: 'VM details check timed out after 15 seconds',
        rawOutput: 'Timeout'
      });
    }, 15000);

    const command = `powershell -Command "try { Get-VM -Name '${vmName}' | ConvertTo-Json -Depth 2 } catch { Write-Output '{\"error\": \"VM not found or access denied\"}' }"`;

    console.log(`[VM Details] Executing command with admin privileges: ${command}`);

    // Try with elevated privileges first
    const options = {
      name: 'UseIt VM Details'
    };

    sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
      clearTimeout(timeoutId);

      console.log(`[VM Details] Sudo command completed`);
      console.log(`[VM Details] Error:`, error);
      console.log(`[VM Details] Stdout:`, stdout);
      console.log(`[VM Details] Stderr:`, stderr);

      if (error) {
        console.warn('[VM Details] Sudo command failed, trying without elevation...');

        // Fallback to regular exec if sudo fails
        exec(command, { timeout: 12000 }, (fallbackError, fallbackStdout, fallbackStderr) => {
          console.log(`[VM Details] Fallback command completed`);
          console.log(`[VM Details] Fallback Error:`, fallbackError);
          console.log(`[VM Details] Fallback Stdout:`, fallbackStdout);
          console.log(`[VM Details] Fallback Stderr:`, fallbackStderr);

          if (fallbackError) {
            console.error(`[VM Details] Fallback command failed: ${fallbackError.message}`);
            resolve({
              success: false,
              error: `Failed to get VM details: ${fallbackError.message}`,
              rawOutput: fallbackStderr || fallbackError.message
            });
            return;
          }

          try {
            const output = fallbackStdout?.toString().trim() || '';
            if (!output) {
              resolve({
                success: false,
                error: 'No output from VM details command',
                rawOutput: 'Empty output'
              });
              return;
            }

            const details = JSON.parse(output);

            // Check if the result contains an error
            if (details.error) {
              resolve({
                success: false,
                error: details.error,
                rawOutput: output
              });
              return;
            }

            resolve({
              success: true,
              details: details,
              rawOutput: output
            });
          } catch (parseError) {
            console.error('[VM Details] Error parsing JSON:', parseError);
            resolve({
              success: false,
              error: 'Failed to parse VM details JSON',
              rawOutput: fallbackStdout?.toString().trim() || '',
              parseError: (parseError as Error).message
            });
          }
        });
        return;
      }

      try {
        const output = stdout?.toString().trim() || '';
        if (!output) {
          resolve({
            success: false,
            error: 'No output from VM details command',
            rawOutput: 'Empty output'
          });
          return;
        }

        const details = JSON.parse(output);

        // Check if the result contains an error
        if (details.error) {
          resolve({
            success: false,
            error: details.error,
            rawOutput: output
          });
          return;
        }

        resolve({
          success: true,
          details: details,
          rawOutput: output
        });
      } catch (parseError) {
        console.error('[VM Details] Error parsing JSON:', parseError);
        resolve({
          success: false,
          error: 'Failed to parse VM details JSON',
          rawOutput: stdout?.toString().trim() || '',
          parseError: (parseError as Error).message
        });
      }
    });
  });
});

// Store active RDP clients to manage connections
const activeRdpClients = new Map<string, any>();

// Fixed RDP Session Management (1-16)
const TOTAL_RDP_SESSIONS = 16;
let rdpSessionsInitialized = false;

// Initialize fixed RDP sessions on startup
async function initializeFixedRdpSessions() {
  if (rdpSessionsInitialized) return;

  try {
    console.log('[RDP Session] Initializing 16 fixed RDP sessions...');

    // Get VM IP first using the same pattern as other functions
    const vmIp = await new Promise((resolve, reject) => {
      const scriptName = 'get-vm-ip.ps1';
      const scriptDir = app.isPackaged
        ? path.join(process.resourcesPath, 'scripts')
        : path.join(app.getAppPath(), 'src/resources/scripts');

      const scriptPath = path.join(scriptDir, scriptName);
      const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName UseIt-Dev-VM`;

      const options = { name: 'UseIt Get VM IP' };

      sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
        if (error) {
          reject(error);
          return;
        }
        if (stderr) {
          reject(new Error(stderr.toString()));
          return;
        }
        const output = stdout ? stdout.toString() : '';
        const successMatch = output.match(/SUCCESS: Found IP address: (.*)/);
        if (successMatch && successMatch[1]) {
          resolve(successMatch[1].trim());
        } else {
          reject(new Error(output.trim() || 'Failed to get VM IP address.'));
        }
      });
    });

    if (!vmIp) {
      console.error('[RDP Session] Failed to get VM IP, cannot initialize sessions');
      return;
    }

    // Pre-create all 16 sessions
    for (let i = 1; i <= TOTAL_RDP_SESSIONS; i++) {
      const sessionId = i.toString();
      console.log(`[RDP Session] Pre-creating session ${sessionId} for VM IP: ${vmIp}`);

      // Here we would create the actual RDP session if needed
      // For now, we just log that the session slot is reserved
    }

    rdpSessionsInitialized = true;
    console.log('[RDP Session] All 16 RDP sessions initialized successfully');

  } catch (error) {
    console.error('[RDP Session] Failed to initialize RDP sessions:', error);
  }
}

// Get fixed session ID (1-16)
function getFixedSessionId(sessionIndex: number): string {
  // Ensure sessionIndex is between 1-16
  const validIndex = Math.max(1, Math.min(16, sessionIndex));
  return validIndex.toString();
}













// Removed unused forceActivateVideoWindow function
// Using the inline implementation in the IPC handler instead








ipcMain.handle('start-rdp-session', async (event, { ip, username, password }) => {
  try {
    console.log(`Starting RDP session to ${ip} with user ${username}`);
    
    // Close existing connection if any
    const existingClient = activeRdpClients.get(ip);
    if (existingClient) {
      console.log('Closing existing RDP connection for', ip);
      existingClient.removeAllListeners();
      existingClient.close();
      activeRdpClients.delete(ip);
    }

    console.log('Creating RDP client with enhanced configuration (clipboard + typing support)...');
    const client = rdp.createClient({
      domain: '',  // Windows域，本地连接为空
      userName: username,
      password: password,
      enablePerf: true,  // 启用性能优化
      autoLogin: true,   // 自动登录
      screen: { width: 1024, height: 768 },
      locale: 'en',      // 键盘布局
      logLevel: 'INFO',  // 与mstsc.js成功配置保持一致
      // 启用剪贴板和设备重定向
      enableClipboard: true,  // 启用剪贴板共享
      enableLocalPrinters: true,  // 启用本地打印机
      enableSmartCards: true,  // 启用智能卡
      // 音频设置
      audioPlayMode: 'local',  // 本地播放音频
      audioCaptureMode: false,  // 不捕获音频
      // 显示优化设置
      showWallpaper: false,  // 不显示壁纸以提高性能
      fontSmoothing: true,  // 启用字体平滑
      desktopComposition: true,  // 启用桌面合成
      showThemes: true,  // 显示主题
      showBlinkingCursor: true  // 显示闪烁光标
    });

    client.on('connect', () => {
      console.log('RDP connected to:', ip);
      // Send connection status to all windows that might be listening
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('rdp-connection-status', { status: 'connected', ip });
      });

      console.log('RDP session established. Automation will be triggered when user switches to RDP tab.');
    });

    client.on('ready', () => {
      console.log('RDP ready for:', ip);
      // Send connection status to all windows that might be listening
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('rdp-connection-status', { status: 'ready', ip });
      });
    });

    client.on('bitmap', (bitmap: any) => {
      console.log(`Received bitmap update: ${bitmap.width}x${bitmap.height} at (${bitmap.destLeft}, ${bitmap.destTop})`);
      // Send bitmap data to all windows that might be listening
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('rdp-bitmap-update', {
          destLeft: bitmap.destLeft,
          destTop: bitmap.destTop,
          destRight: bitmap.destRight,
          destBottom: bitmap.destBottom,
          width: bitmap.width,
          height: bitmap.height,
          bitsPerPixel: bitmap.bitsPerPixel,
          isCompress: bitmap.isCompress,
          data: Array.from(bitmap.data) // Convert Buffer to array for IPC
        });
      });
    });

    client.on('close', () => {
      console.log('RDP closed for:', ip);
      activeRdpClients.delete(ip);
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('rdp-connection-status', { status: 'closed', ip });
      });
    });

    client.on('error', (err: Error) => {
      console.error('RDP error for', ip, ':', err);
      console.error('Error details:', err.stack);
      activeRdpClients.delete(ip);
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('rdp-connection-status', { 
          status: 'error', 
          ip, 
          error: err.message 
        });
      });
    });

    // Store the client for management
    activeRdpClients.set(ip, client);

    console.log(`Attempting to connect to RDP server at ${ip}:3389...`);
    
    // Connect to the RDP server
    client.connect(ip, 3389);

    console.log('RDP connect() called, waiting for connection events...');
    return { success: true, message: 'RDP connection initiated' };
  } catch (error) {
    console.error('Failed to start RDP session:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Handle RDP session termination
ipcMain.handle('stop-rdp-session', async (event, { ip }) => {
  try {
    const client = activeRdpClients.get(ip);
    if (client) {
      client.removeAllListeners();
      client.close();
      activeRdpClients.delete(ip);
      return { success: true, message: 'RDP session terminated' };
    }
    return { success: false, error: 'No active RDP session found' };
  } catch (error) {
    console.error('Failed to stop RDP session:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Store RDP credentials securely for session
const rdpCredentials = new Map<string, { username: string; password: string }>();

// Handle opening RDP view in popup window
ipcMain.handle('open-rdp-view', async (event, { ip, username, password }) => {
  try {
    // Store credentials securely for this session
    rdpCredentials.set(ip, { username, password });
    console.log(`RDP credentials stored for IP: ${ip}`);
    
    // Create RDP popup window
    const rdpWindow = RdpPopupWindow.create(ip);
    
    return { success: true, message: 'RDP popup window opened' };
  } catch (error) {
    console.error('Failed to open RDP popup:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Handle getting RDP credentials for a specific IP
ipcMain.handle('get-rdp-credentials', async (event, { ip }) => {
  const credentials = rdpCredentials.get(ip);
  if (credentials) {
    return { success: true, credentials };
  }
  return { success: false, error: 'No credentials found for this IP' };
});

// Handle getting RDP URL for iframe access
ipcMain.handle('get-rdp-url', async (event, { ip }) => {
  try {
    const url = rdpServer.getUrl(ip);
    return { success: true, url };
  } catch (error) {
    console.error('Failed to get RDP URL:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Handle testing TCP connection
ipcMain.handle('test-tcp-connection', async (event, { ip, port = 3389 }) => {
  try {
    console.log('Starting TCP connection test...');
    const result = await RdpTest.testTcpConnection(ip, port);
    console.log('TCP test completed successfully:', result);
    return result;
  } catch (error) {
    console.error('TCP test failed:', error);
    return error;
  }
});

// Handle testing RDP connection
ipcMain.handle('test-rdp-connection', async (event, { ip, username, password }) => {
  try {
    console.log('Starting RDP connection test...');
    
    // 首先测试TCP连接
    console.log('Step 1: Testing TCP connection...');
    await RdpTest.testTcpConnection(ip, 3389);
    console.log('Step 2: TCP connection successful, now testing RDP...');
    
    const result = await RdpTest.testConnection(ip, username, password);
    console.log('RDP test completed successfully:', result);
    return result;
  } catch (error) {
    console.error('RDP test failed:', error);
    return error;
  }
});

// VM Client APIs - File transfer and command execution
ipcMain.handle('vm-client-upload-file', async (event, { ip, fileData, filePath, filename }) => {
  try {
    console.log(`[VM Client] Uploading file to ${ip}:3000 - ${filename} -> ${filePath}`);

    const response = await fetch(`http://${ip}:3000/uploadFile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_data: fileData,
        file_path: filePath,
        filename: filename
      }),
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[VM Client] File upload result:`, result);

    return {
      success: true,
      result: result,
      message: `File uploaded successfully to ${filePath}`
    };

  } catch (error) {
    console.error('[VM Client] File upload error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: `Failed to upload file to VM at ${ip}:3000`
    };
  }
});

ipcMain.handle('vm-client-execute-command', async (event, { ip, command, workingDir, timeout = 30 }) => {
  try {
    console.log(`[VM Client] Executing command on ${ip}:3000 - ${command}`);

    const requestBody: any = {
      command: command,
      timeout: timeout
    };

    if (workingDir) {
      requestBody.working_dir = workingDir;
    }

    const response = await fetch(`http://${ip}:3000/executeCommand`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout((timeout + 5) * 1000) // Add 5 seconds buffer to timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[VM Client] Command execution result:`, result);

    return {
      success: true,
      result: result,
      message: `Command executed successfully: ${command}`
    };

  } catch (error) {
    console.error('[VM Client] Command execution error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: `Failed to execute command on VM at ${ip}:3000`
    };
  }
});

// Get fixed RDP session (1-16) with VM IP
ipcMain.handle('get-persistent-rdp-session', async (event, { vmName, sessionIndex, ip, username, password }) => {
  try {
    console.log(`[RDP Session] Getting fixed session for ${vmName} session ${sessionIndex}`);

    const sessionId = getFixedSessionId(sessionIndex);

    // Get VM IP if not provided
    let vmIp = ip;
    if (!vmIp || vmIp === '') {
      console.log(`[RDP Session] Getting VM IP for ${vmName}`);
      try {
        vmIp = await new Promise((resolve, reject) => {
          const scriptName = 'get-vm-ip.ps1';
          const scriptDir = app.isPackaged
            ? path.join(process.resourcesPath, 'scripts')
            : path.join(app.getAppPath(), 'src/resources/scripts');

          const scriptPath = path.join(scriptDir, scriptName);
          const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName ${vmName}`;

          const options = {
            name: 'UseIt Get VM IP'
          };

          sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
            if (error) {
              reject(error);
              return;
            }
            if (stderr) {
              reject(new Error(stderr.toString()));
              return;
            }
            const output = stdout ? stdout.toString() : '';
            const successMatch = output.match(/SUCCESS: Found IP address: (.*)/);
            if (successMatch && successMatch[1]) {
              resolve(successMatch[1].trim());
            } else {
              reject(new Error(output.trim() || 'Failed to get VM IP address.'));
            }
          });
        });
        console.log(`[RDP Session] Got VM IP: ${vmIp}`);
      } catch (error: any) {
        console.error(`[RDP Session] Failed to get VM IP: ${error.message}`);
        return {
          success: false,
          error: `Failed to get VM IP: ${error.message}`
        };
      }
    }

    console.log(`[RDP Session] Returning fixed session ID: ${sessionId} with IP: ${vmIp}`);

    return {
      success: true,
      sessionId: sessionId,
      ip: vmIp,
      sessionInfo: {
        vmName: vmName,
        sessionIndex: sessionIndex,
        sessionId: sessionId,
        ip: vmIp,
        isFixed: true
      }
    };
  } catch (error: any) {
    console.error('[RDP Session] Error getting fixed session:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Get all fixed RDP sessions status (1-16)
ipcMain.handle('get-rdp-sessions-status', async (event) => {
  try {
    const sessions = [];
    for (let i = 1; i <= TOTAL_RDP_SESSIONS; i++) {
      sessions.push({
        sessionId: i.toString(),
        sessionIndex: i,
        status: 'available',
        description: `Fixed RDP Session ${i}`,
        isFixed: true
      });
    }

    return {
      success: true,
      sessions: sessions,
      total: TOTAL_RDP_SESSIONS,
      message: `16 fixed RDP sessions available`
    };
  } catch (error: any) {
    console.error('[RDP Session] Error getting sessions status:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Check VM port status
ipcMain.handle('check-vm-port', async (event, { vmName, port }) => {
  try {
    console.log(`[Check VM Port] Checking port ${port} on VM: ${vmName}`);

    // Get VM IP address
    const vmIp = await new Promise((resolve, reject) => {
      const scriptName = 'get-vm-ip.ps1';
      const scriptDir = app.isPackaged
        ? path.join(process.resourcesPath, 'scripts')
        : path.join(__dirname, '../../scripts');
      const scriptPath = path.join(scriptDir, scriptName);

      const command = `powershell.exe -ExecutionPolicy Bypass -File "${scriptPath}" -VMName "${vmName}"`;

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error(`[Check VM Port] PowerShell error: ${error.message}`);
          reject(error);
          return;
        }

        if (stderr) {
          console.error(`[Check VM Port] PowerShell stderr: ${stderr}`);
          reject(new Error(stderr));
          return;
        }

        const ip = stdout.trim();
        if (!ip || ip === 'null' || ip === '') {
          reject(new Error('No IP address returned from PowerShell script'));
          return;
        }

        console.log(`[Check VM Port] Got VM IP: ${ip}`);
        resolve(ip);
      });
    });

    console.log(`[Check VM Port] Got VM IP: ${vmIp}`);

    // Try to connect to the port
    const testUrl = `http://${vmIp}:${port}/`;

    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      return {
        success: true,
        vmIp: vmIp,
        port: port,
        status: 'open',
        httpStatus: response.status,
        message: `Port ${port} is accessible (HTTP ${response.status})`
      };
    } catch (fetchError: any) {
      return {
        success: false,
        vmIp: vmIp,
        port: port,
        status: 'closed',
        error: fetchError.message,
        message: `Port ${port} is not accessible: ${fetchError.message}`
      };
    }

  } catch (error: any) {
    console.error('[Check VM Port] Error:', error);
    return {
      success: false,
      error: error.message,
      message: `Failed to check VM port: ${error.message}`
    };
  }
});

// Execute action on local VM
ipcMain.handle('execute-local-vm-action', async (event, { action, runInstanceId }) => {
  try {
    console.log(`[Local VM Action] Executing action for run ${runInstanceId}:`, action);

    // Get VM IP address
    const vmIp = await new Promise((resolve, reject) => {
      const scriptName = 'get-vm-ip.ps1';
      const scriptDir = app.isPackaged
        ? path.join(process.resourcesPath, 'scripts')
        : path.join(app.getAppPath(), 'src/resources/scripts');

      const scriptPath = path.join(scriptDir, scriptName);
      const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName UseIt-Dev-VM`;

      const options = {
        name: 'UseIt Get VM IP'
      };

      sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
        if (error) {
          reject(error);
          return;
        }
        if (stderr) {
          reject(new Error(stderr.toString()));
          return;
        }
        const output = stdout ? stdout.toString() : '';
        const successMatch = output.match(/SUCCESS: Found IP address: (.*)/);
        if (successMatch && successMatch[1]) {
          resolve(successMatch[1].trim());
        } else {
          reject(new Error(output.trim() || 'Failed to get VM IP address.'));
        }
      });
    });

    console.log(`[Local VM Action] Got VM IP: ${vmIp}`);

    // Send action to VM on port 7889
    const vmUrl = `http://${vmIp}:7889/execute_action`;
    const payload = {
      generated_action: action,
      run_instance_id: runInstanceId,
      mode: 'local_vm'
    };

    console.log(`[Local VM Action] Sending to ${vmUrl}:`, payload);

    const response = await fetch(vmUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(30000)
    });

    if (!response.ok) {
      throw new Error(`VM returned status ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[Local VM Action] VM response:`, result);

    return {
      success: true,
      result: result,
      vmIp: vmIp,
      message: `Action executed on Local VM at ${vmIp}:7889`
    };

  } catch (error) {
    console.error('[Local VM Action] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Failed to execute action on local VM'
    };
  }
});

// Clean up on app quit
app.on('before-quit', () => {
  rdpServer.stop();

  // Recording is now managed by HTTP API service
  console.log('App quit - recording managed by external service');
});

ipcMain.handle(
  'navigate-to-url',
  (event: IpcMainInvokeEvent, url: string) => {
    const win = BrowserWindow.fromWebContents(event.sender)
    if (win && url) {
      win.loadURL(url)
    }
  },
)

// Start recording using HTTP API
ipcMain.handle('start-recording', async () => {
  console.log('[Start Recording] Calling recorder API...');

  try {
    const response = await fetch('http://localhost:3000/startRecording', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('[Start Recording] API response:', data);

      return {
        success: true,
        message: 'Recording started successfully',
        data: data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API returned status ${response.status}: ${errorText}`,
        statusCode: response.status
      };
    }
  } catch (error) {
    console.error('[Start Recording] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Make sure recorder service is running on port 3000'
    };
  }
})

// Stop recording using HTTP API
ipcMain.handle('stop-recording', async () => {
  console.log('[Stop Recording] Calling recorder API...');

  try {
    const response = await fetch('http://localhost:3000/stopRecording', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('[Stop Recording] API response:', data);

      return {
        success: true,
        message: 'Recording stopped successfully',
        data: data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API returned status ${response.status}: ${errorText}`,
        statusCode: response.status
      };
    }
  } catch (error) {
    console.error('[Stop Recording] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Make sure recorder service is running on port 3000'
    };
  }
})

// Get recording status using HTTP API
ipcMain.handle('get-recording-status', async () => {
  console.log('[Get Status] Calling recorder API...');

  try {
    const response = await fetch('http://localhost:3000/getStatus');

    if (response.ok) {
      const data = await response.json();
      console.log('[Get Status] API response:', data);

      return {
        success: true,
        message: 'Status retrieved successfully',
        status: data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API returned status ${response.status}: ${errorText}`,
        statusCode: response.status
      };
    }
  } catch (error) {
    console.error('[Get Status] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Make sure recorder service is running on port 3000'
    };
  }
})

// Get recorded file path and upload using HTTP API
ipcMain.handle('upload-recording', async () => {
  console.log('[Upload Recording] Getting recorded file path...');

  try {
    // First get the recorded file path
    const pathResponse = await fetch('http://localhost:3000/getRecordedFilePath');

    if (!pathResponse.ok) {
      const errorText = await pathResponse.text();
      return {
        success: false,
        error: `Failed to get file path: ${pathResponse.status} ${errorText}`,
        statusCode: pathResponse.status
      };
    }

    const pathData = await pathResponse.json();
    console.log('[Upload Recording] File path response:', pathData);

    if (!pathData.filePath || !fs.existsSync(pathData.filePath)) {
      return {
        success: false,
        error: 'Recorded file not found or path is invalid',
        filePath: pathData.filePath
      };
    }

    // Get file stats
    const stats = fs.statSync(pathData.filePath);

    // For now, just return the file info ready for upload
    // In a real implementation, you would upload to a server here
    return {
      success: true,
      message: 'Video file ready for upload',
      videoFile: {
        name: path.basename(pathData.filePath),
        path: pathData.filePath,
        size: stats.size,
        createdAt: stats.mtime
      }
    };

  } catch (error) {
    console.error('[Upload Recording] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Make sure recorder service is running on port 3000'
    };
  }
})

// Test video.exe and start it if needed
ipcMain.handle('test-video-exe', async () => {
  console.log('[Test Video.exe] Testing video.exe and starting if needed...');

  try {
    // First test if the recorder API is already running
    try {
      const response = await fetch('http://localhost:3000/getStatus');
      if (response.ok) {
        const data = await response.json();
        console.log('[Test Video.exe] Recorder service is already running, status:', data);
        return {
          success: true,
          message: 'Recorder service is already running',
          alreadyRunning: true,
          status: data
        };
      }
    } catch (error) {
      // Service not running, continue to start video.exe
      console.log('[Test Video.exe] Recorder service not accessible, will start video.exe');
    }

    // Determine video.exe path
    const videoExePath = app.isPackaged
      ? path.join(process.resourcesPath, 'bin', 'video.exe')
      : path.join(app.getAppPath(), 'src', 'resources', 'bin', 'video.exe');

    console.log('[Test Video.exe] Video.exe path:', videoExePath);

    // Check if video.exe exists
    if (!require('fs').existsSync(videoExePath)) {
      return {
        success: false,
        error: 'video.exe not found',
        path: videoExePath,
        details: 'Make sure video.exe is in the correct location'
      };
    }

    // Start video.exe
    console.log('[Test Video.exe] Starting video.exe...');
    const { spawn } = require('child_process');

    const videoProcess = spawn(videoExePath, [], {
      detached: true,
      stdio: 'ignore'
    });

    // Don't wait for the process to exit
    videoProcess.unref();

    // Wait a moment for the service to start
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test if the service is now running with multiple attempts
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`[Test Video.exe] Testing service availability, attempt ${attempt}/3`);
        const testResponse = await fetch('http://localhost:3000/getStatus');
        if (testResponse.ok) {
          const data = await testResponse.json();
          console.log('[Test Video.exe] Video.exe started successfully, status:', data);
          return {
            success: true,
            message: 'video.exe started successfully',
            path: videoExePath,
            status: data
          };
        } else {
          console.log(`[Test Video.exe] Attempt ${attempt}: Service responded with status ${testResponse.status}`);
        }
      } catch (error) {
        console.log(`[Test Video.exe] Attempt ${attempt}: Service not accessible yet`);
      }

      // Wait before next attempt
      if (attempt < 3) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return {
      success: false,
      error: 'video.exe started but service not accessible after multiple attempts',
      details: 'Service may need more time to start up'
    };

  } catch (error) {
    console.error('[Test Video.exe] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Failed to start video.exe'
    };
  }
})

// Read recorded file from disk - Modified to handle large files
ipcMain.handle('read-recorded-file', async (event, { filePath }) => {
  console.log('[Read Recorded File] Reading file from:', filePath);

  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('[Read Recorded File] File not found:', filePath);
      return {
        success: false,
        error: 'File not found',
        filePath
      };
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    console.log('[Read Recorded File] File size:', stats.size, 'bytes');

    // Check file size limit (100MB = 104857600 bytes)
    const maxFileSize = 100 * 1024 * 1024; // 100MB
    if (stats.size > maxFileSize) {
      console.error('[Read Recorded File] File too large:', stats.size, 'bytes');
      return {
        success: false,
        error: 'File too large',
        details: `File size ${Math.round(stats.size / 1024 / 1024)}MB exceeds limit of ${Math.round(maxFileSize / 1024 / 1024)}MB`,
        fileSize: stats.size
      };
    }

    // Read file content and convert to base64
    console.log('[Read Recorded File] Reading file content...');
    const fileContent = fs.readFileSync(filePath);
    const base64Data = fileContent.toString('base64');

    // Use backend-compatible MIME type for MKV files
    const fileName = path.basename(filePath);
    const fileExtension = path.extname(fileName).toLowerCase();
    const mimeType = 'video/mkv'; // 使用后端明确支持的 video/mkv 格式

    console.log(`[Read Recorded File] File: ${fileName}, Extension: ${fileExtension}, Size: ${stats.size} bytes, MIME type: ${mimeType}`);
    console.log(`[Read Recorded File] Base64 data length: ${base64Data.length} characters`);

    return {
      success: true,
      fileName: fileName,
      fileSize: stats.size,
      mimeType: mimeType,
      base64Data: base64Data, // Return actual file content as base64
      filePath: filePath, // Also include file path for reference
    };
  } catch (error) {
    console.error('[Read Recorded File] Error reading file:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Failed to read recorded file'
    };
  }
})

// Test recorder API connection
ipcMain.handle('test-recorder-api', async () => {
  console.log('[Test Recorder] Testing recorder API connection...');

  try {
    const response = await fetch('http://localhost:3000/getStatus');

    if (response.ok) {
      const data = await response.json();
      console.log('[Test Recorder] API response:', data);

      return {
        success: true,
        message: 'Recorder API is accessible',
        status: data
      };
    } else {
      return {
        success: false,
        error: `API returned status ${response.status}`,
        statusCode: response.status
      };
    }
  } catch (error) {
    console.error('[Test Recorder] Error:', error);
    return {
      success: false,
      error: (error as Error).message,
      details: 'Make sure recorder service is running on port 3000'
    };
  }
})

// Handle VM diagnostics
ipcMain.handle('diagnose-vm-environment', async () => {
  console.log('[VM Diagnostics] Starting environment diagnostics...');

  const diagnostics = {
    platform: process.platform,
    hyperVAvailable: false,
    hyperVEnabled: false,
    vmExists: false,
    vmList: [] as any[],
    errors: [] as string[]
  };

  try {
    // Check if Hyper-V feature is available with admin privileges
    const hyperVCheckCommand = `powershell -ExecutionPolicy Bypass -Command "try { Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All | Select-Object -ExpandProperty State } catch { Write-Output 'NotAvailable' }"`;

    console.log('[VM Diagnostics] Checking Hyper-V with admin privileges...');

    const hyperVResult = await new Promise((resolve) => {
      const options = {
        name: 'UseIt VM Diagnostics'
      };

      sudo.exec(hyperVCheckCommand, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
        if (error) {
          console.warn('[VM Diagnostics] Admin Hyper-V check failed, trying without elevation...');
          // Fallback to regular exec
          exec(hyperVCheckCommand, { timeout: 10000 }, (_fallbackError, fallbackStdout, _fallbackStderr) => {
            const state = fallbackStdout?.toString().trim() || 'NotAvailable';
            console.log('[VM Diagnostics] Fallback Hyper-V check result:', state);
            resolve(state);
          });
        } else {
          const state = stdout?.toString().trim() || 'NotAvailable';
          console.log('[VM Diagnostics] Admin Hyper-V check result:', state);
          resolve(state);
        }
      });
    });

    diagnostics.hyperVAvailable = hyperVResult !== 'NotAvailable';
    diagnostics.hyperVEnabled = hyperVResult === 'Enabled';

    console.log('[VM Diagnostics] Hyper-V Available:', diagnostics.hyperVAvailable);
    console.log('[VM Diagnostics] Hyper-V Enabled:', diagnostics.hyperVEnabled);

    if (diagnostics.hyperVEnabled) {
      // List all VMs with admin privileges
      const listVMsCommand = `powershell -ExecutionPolicy Bypass -Command "try { Get-VM | Select-Object Name, State | ConvertTo-Json } catch { Write-Output '[]' }"`;

      console.log('[VM Diagnostics] Listing VMs with admin privileges...');

      const vmListResult = await new Promise((resolve) => {
        const options = {
          name: 'UseIt VM List'
        };

        sudo.exec(listVMsCommand, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
          if (error) {
            console.warn('[VM Diagnostics] Admin VM list failed, trying without elevation...');
            // Fallback to regular exec
            exec(listVMsCommand, { timeout: 15000 }, (_fallbackError, fallbackStdout, _fallbackStderr) => {
              try {
                const vmList = JSON.parse(fallbackStdout?.toString().trim() || '[]');
                const result = Array.isArray(vmList) ? vmList : [vmList];
                console.log('[VM Diagnostics] Fallback VM list result:', result);
                resolve(result);
              } catch {
                console.log('[VM Diagnostics] Fallback VM list parse failed');
                resolve([]);
              }
            });
          } else {
            try {
              const vmList = JSON.parse(stdout?.toString().trim() || '[]');
              const result = Array.isArray(vmList) ? vmList : [vmList];
              console.log('[VM Diagnostics] Admin VM list result:', result);
              resolve(result);
            } catch {
              console.log('[VM Diagnostics] Admin VM list parse failed');
              resolve([]);
            }
          }
        });
      });

      diagnostics.vmList = vmListResult as any[];
      diagnostics.vmExists = diagnostics.vmList.some((vm: any) => vm.Name === 'UseIt-Dev-VM');

      console.log('[VM Diagnostics] VM List:', diagnostics.vmList);
      console.log('[VM Diagnostics] UseIt-Dev-VM Exists:', diagnostics.vmExists);
    }

  } catch (error: any) {
    diagnostics.errors.push(error.message);
  }

  console.log('[VM Diagnostics] Results:', diagnostics);
  return diagnostics;
})

ipcMain.on('window-minimize', (event: IpcMainInvokeEvent) => {
  const win = BrowserWindow.fromWebContents(event.sender)
  win?.minimize()
})

ipcMain.on('window-maximize', (event: IpcMainInvokeEvent) => {
  const win = BrowserWindow.fromWebContents(event.sender)
  if (win?.isMaximized()) {
    win.unmaximize()
  } else {
    win?.maximize()
  }
})

ipcMain.on('window-close', (event: IpcMainInvokeEvent) => {
  const win = BrowserWindow.fromWebContents(event.sender)
  win?.close()
})

// AI Run Relay Handlers
// Start AI Run with relay to local VM
ipcMain.handle('ai-run-relay-start', async (event, { workflowId, targetPcUrl, message, metadata }) => {
  try {
    console.log('[AI Run Relay] Starting AI run with relay:', { workflowId, targetPcUrl, message, metadata });

    // Get backend API URL from environment or use default
    const backendUrl = process.env.BACKEND_API_URL || 'http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com:8888';
    const token = metadata?.token || '';

    // Step 1: Start AI run on backend
    const startResponse = await fetch(`${backendUrl}/console/api/ai-run/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        workflowId,
        target_pc_url: targetPcUrl,
        message: message.trim(),
        metadata,
      }),
    });

    if (!startResponse.ok) {
      throw new Error(`Failed to start AI run: ${startResponse.statusText}`);
    }

    const startData = await startResponse.json();
    console.log('[AI Run Relay] Backend AI run started:', startData);

    if (startData.status === 'success' && startData.runInstanceId) {
      return {
        success: true,
        runInstanceId: startData.runInstanceId,
        message: 'AI run started successfully with relay'
      };
    } else {
      throw new Error(startData.message || 'Failed to start AI run');
    }

  } catch (error) {
    console.error('[AI Run Relay] Start error:', error);
    return {
      success: false,
      error: (error as Error).message,
      message: 'Failed to start AI run with relay'
    };
  }
});



// Send message to AI Run with relay to local VM
ipcMain.handle('ai-run-relay-message', async (event, { runInstanceId, message, metadata }) => {
  console.log('=== AI RUN RELAY MESSAGE HANDLER CALLED ===');
  console.log('Parameters received:', { runInstanceId, message, metadata: metadata ? 'present' : 'missing' });

  const startTime = Date.now();
  let cachedVmIp = null; // Cache VM IP for reuse
  try {
    console.log('[AI Run Relay] 🚀 Starting message relay process...');
    console.log('[AI Run Relay] 📝 Request details:', { runInstanceId, message, metadata: { ...metadata, token: metadata?.token ? '[HIDDEN]' : 'none' } });

    // Get backend API URL from environment or use default
    const backendUrl = process.env.BACKEND_API_URL || 'http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com:8888';
    const token = metadata?.token || '';
    const apiUrl = `${backendUrl}/console/api/ai-run/runs/${runInstanceId}/dispatch_input`;

    console.log('[AI Run Relay] 🌐 Calling backend API:', apiUrl);
    console.log(`[AI Run Relay] ⏱️ Step 1 start: Backend API call (${Date.now() - startTime}ms elapsed)`);

    // Step 1: Send message to backend AI run with longer timeout
    const messageResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        content: message.trim(),
      }),
      signal: AbortSignal.timeout(90000) // 90 second timeout for backend
    });

    console.log('[AI Run Relay] 📡 Backend response status:', messageResponse.status, messageResponse.statusText);
    console.log(`[AI Run Relay] ⏱️ Step 1 complete: Backend responded (${Date.now() - startTime}ms elapsed)`);

    if (!messageResponse.ok) {
      const errorText = await messageResponse.text();
      console.error('[AI Run Relay] ❌ Backend error response:', errorText);
      throw new Error(`Failed to send message to AI run: ${messageResponse.status} ${messageResponse.statusText} - ${errorText}`);
    }

    let messageData = await messageResponse.json();

    // Log backend response but truncate large data for readability
    const logData = { ...messageData };
    if (logData.response_data?.text && logData.response_data.text.length > 200) {
      logData.response_data.text = `[TEXT_${logData.response_data.text.length}_CHARS]`;
    }
    if (logData.base64_image && logData.base64_image.length > 100) {
      logData.base64_image = `[BASE64_IMAGE_${logData.base64_image.length}_CHARS]`;
    }
    console.log('[AI Run Relay] 📨 Backend message response:', JSON.stringify(logData, null, 2));

    // Step 2: If backend returns a complete response with action, forward to local VM
    // Check multiple possible response structures (backend uses nested structure)
    const generatedAction = messageData.generate_action?.generated_action ||
                           messageData.generate_action ||
                           messageData["generate action"] ||
                           messageData.generated_action ||
                           messageData.ai_run_response?.generated_action;

    console.log('[AI Run Relay] 🔍 Checking for generated_action...');
    console.log('[AI Run Relay] 🔍 messageData.status:', messageData.status);
    console.log('[AI Run Relay] 🔍 messageData keys:', Object.keys(messageData));
    console.log('[AI Run Relay] 🔍 messageData.generate_action:', !!messageData.generate_action);
    console.log('[AI Run Relay] 🔍 messageData.generate_action?.generated_action:', !!messageData.generate_action?.generated_action);
    console.log('[AI Run Relay] 🔍 messageData["generate action"]:', !!messageData["generate action"]);
    console.log('[AI Run Relay] 🔍 messageData.generated_action:', !!messageData.generated_action);
    console.log('[AI Run Relay] 🔍 generatedAction found:', !!generatedAction);
    if (generatedAction) {
      console.log('[AI Run Relay] 🔍 generatedAction.method:', generatedAction.method);
      console.log('[AI Run Relay] 🔍 generatedAction.action:', generatedAction.action);
      console.log('[AI Run Relay] 🔍 generatedAction full object:', JSON.stringify(generatedAction, null, 2));
    } else {
      console.log('[AI Run Relay] ❌ No generatedAction found - will return original response without VM forwarding');
    }

    if (messageData.status === 'success' && generatedAction) {
      console.log('[AI Run Relay] ✅ Backend response contains generated_action, forwarding to local VM...');
      console.log('[AI Run Relay] 📋 Generated action:', JSON.stringify(generatedAction, null, 2));
      console.log(`[AI Run Relay] ⏱️ Step 2 start: VM forwarding (${Date.now() - startTime}ms elapsed)`);

      // Get VM IP address with timeout (cache for reuse)
      const vmIp = await Promise.race([
        new Promise((resolve, reject) => {
          const scriptName = 'get-vm-ip.ps1';
          const scriptDir = app.isPackaged
            ? path.join(process.resourcesPath, 'scripts')
            : path.join(app.getAppPath(), 'src/resources/scripts');

          const scriptPath = path.join(scriptDir, scriptName);
          const command = `powershell -ExecutionPolicy Bypass -File "${scriptPath}" -VMName UseIt-Dev-VM`;

          const options = { name: 'UseIt Get VM IP' };

          sudo.exec(command, options, (error?: Error, stdout?: string | Buffer, stderr?: string | Buffer) => {
            if (error) {
              reject(error);
              return;
            }
            if (stderr) {
              reject(new Error(stderr.toString()));
              return;
            }
            const output = stdout ? stdout.toString() : '';
            const successMatch = output.match(/SUCCESS: Found IP address: (.*)/);
            if (successMatch && successMatch[1]) {
              resolve(successMatch[1].trim());
            } else {
              reject(new Error(output.trim() || 'Failed to get VM IP address.'));
            }
          });
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('VM IP lookup timeout after 15 seconds')), 15000)
        )
      ]);

      console.log(`[AI Run Relay] 🌐 Got VM IP: ${vmIp}`);
      console.log(`[AI Run Relay] ⏱️ VM IP obtained (${Date.now() - startTime}ms elapsed)`);

      // Cache the VM IP for later use
      cachedVmIp = vmIp;

      // Determine the correct VM endpoint based on action method
      const actionMethod = generatedAction.method || generatedAction.action;
      let vmUrl, vmPayload, httpMethod;

      console.log(`[AI Run Relay] 🎯 Action method determined: ${actionMethod}`);

      if (actionMethod === 'get_screenshot') {
        // For screenshot, use simple GET endpoint
        vmUrl = `http://${vmIp}:7889/get_screenshot`;
        vmPayload = null; // GET request, no body
        httpMethod = 'GET';

        console.log(`[AI Run Relay] 📸 Screenshot request to VM: ${vmUrl}`);
      } else {
        // For other actions, use execute_action endpoint
        vmUrl = `http://${vmIp}:7889/execute_action`;
        vmPayload = {
          generated_action: generatedAction,
          run_instance_id: runInstanceId,
          mode: 'local_vm',
          complete_response: messageData
        };
        httpMethod = 'POST';

        console.log(`[AI Run Relay] 🚀 Action execution to VM: ${vmUrl}`);
        console.log(`[AI Run Relay] 📦 VM Payload:`, JSON.stringify(vmPayload, null, 2));
      }

      const fetchOptions: any = {
        method: httpMethod
      };

      if (vmPayload) {
        fetchOptions.headers = {
          'Content-Type': 'application/json',
        };
        fetchOptions.body = JSON.stringify(vmPayload);
      }

      const vmResponse = await fetch(vmUrl, fetchOptions);
      console.log(`[AI Run Relay] ⏱️ VM request complete (${Date.now() - startTime}ms elapsed)`);

      if (!vmResponse.ok) {
        console.warn(`[AI Run Relay] ❌ VM returned status ${vmResponse.status}: ${vmResponse.statusText}`);
        const errorText = await vmResponse.text();
        console.warn(`[AI Run Relay] ❌ VM error response:`, errorText);
      } else {
        const vmResult = await vmResponse.json();

        // Log VM result but hide base64 data completely
        if (vmResult.dimensions) {
          console.log(`[AI Run Relay] ✅ VM ${actionMethod} response: Screenshot captured (${vmResult.dimensions.width}x${vmResult.dimensions.height})`);
        } else {
          const logResult = { ...vmResult };
          if (logResult.base64_image) {
            logResult.base64_image = `[BASE64_IMAGE_${logResult.base64_image.length}_CHARS]`;
          }
          console.log(`[AI Run Relay] ✅ VM ${actionMethod} response:`, JSON.stringify(logResult, null, 2));
        }

        // Send VM result back to AI run as a new message
        console.log(`[AI Run Relay] 🔄 Sending VM result back to AI run...`);
        console.log(`[AI Run Relay] ⏱️ Step 3 start: Sending VM result to backend (${Date.now() - startTime}ms elapsed)`);
        try {
          let resultMessage = '';
          if (actionMethod === 'get_screenshot') {
            if (vmResult.status === 'success') {
              resultMessage = `Screenshot captured successfully. Dimensions: ${vmResult.dimensions?.width}x${vmResult.dimensions?.height}. Screenshot data is available for analysis.`;
            } else {
              resultMessage = `Screenshot capture failed: ${vmResult.message || 'Unknown error'}`;
            }
            console.log(`[AI Run Relay] 📸 Screenshot result: ${resultMessage}`);
          } else {
            resultMessage = `Action "${actionMethod}" executed successfully. Result: ${JSON.stringify(vmResult)}`;
            console.log(`[AI Run Relay] ⚡ Action result: ${resultMessage}`);
          }

          console.log(`[AI Run Relay] 🔍 About to send VM result back to backend...`);
          console.log(`[AI Run Relay] 🔍 resultMessage: ${resultMessage}`);
          console.log(`[AI Run Relay] 🔍 actionMethod: ${actionMethod}`);
          console.log(`[AI Run Relay] 🔍 vmResult keys: ${Object.keys(vmResult)}`);
          console.log(`[AI Run Relay] 🔍 vmResult.base64_image exists: ${!!vmResult.base64_image}`);
          console.log(`[AI Run Relay] 🔍 vmResult.screenshot exists: ${!!vmResult.screenshot}`);

          // Send the result back to AI run with correct metadata format
          console.log(`[AI Run Relay] 🌐 Sending VM result to backend: ${backendUrl}/console/api/ai-run/runs/${runInstanceId}/dispatch_input`);
          const payloadForLog = {
            content: resultMessage,
            metadata: {
              source: 'vm_execution_result',
              action_method: actionMethod,
              vm_response: {
                screenshot: vmResult.screenshot ? `[SCREENSHOT_${vmResult.screenshot.length}_CHARS]` : 'none',
                status: vmResult.status,
                dimensions: vmResult.dimensions,
                message: vmResult.message
              }
            }
          };
          console.log(`[AI Run Relay] 📦 VM result payload:`, JSON.stringify(payloadForLog, null, 2));

          const resultResponse = await fetch(`${backendUrl}/console/api/ai-run/runs/${runInstanceId}/dispatch_input`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              content: resultMessage,
              metadata: {
                source: 'vm_execution_result',
                action_method: actionMethod,
                vm_response: {
                  screenshot: vmResult.screenshot || vmResult.base64_image,
                  ...vmResult
                }
              }
            }),
            signal: AbortSignal.timeout(60000) // Increased to 60 seconds
          });

          console.log(`[AI Run Relay] ⏱️ Step 3 complete: Backend VM result request sent (${Date.now() - startTime}ms elapsed)`);

          if (resultResponse.ok) {
            console.log(`[AI Run Relay] ✅ VM result sent back to AI run successfully`);
            console.log(`[AI Run Relay] 📨 Backend response status: ${resultResponse.status} ${resultResponse.statusText}`);

            // ✅ CRITICAL FIX: Get the NEW response from backend after sending VM result
            console.log(`[AI Run Relay] 📥 Reading backend response JSON...`);
            const newBackendResponse = await resultResponse.json();
            console.log(`[AI Run Relay] ⏱️ Step 3 JSON parsed (${Date.now() - startTime}ms elapsed)`);
            console.log(`[AI Run Relay] 📨 NEW Backend response after VM result:`);

            // Log new response but truncate large data for readability
            const logNewData = { ...newBackendResponse };
            if (logNewData.response_data?.text && logNewData.response_data.text.length > 200) {
              logNewData.response_data.text = `[TEXT_${logNewData.response_data.text.length}_CHARS]`;
            }
            console.log(JSON.stringify(logNewData, null, 2));

            // Update messageData with the NEW response that contains the real action
            messageData = newBackendResponse;
            console.log(`[AI Run Relay] 🔄 Updated messageData with new backend response`);

            // ✅ CRITICAL: Re-check for new actions in the updated response
            const newGeneratedAction = messageData.generate_action?.generated_action ||
                                     messageData.generate_action ||
                                     messageData["generate action"] ||
                                     messageData.generated_action ||
                                     messageData.ai_run_response?.generated_action;

            console.log('[AI Run Relay] 🔍 Re-checking NEW response for generated_action...');
            console.log('[AI Run Relay] 🔍 NEW messageData keys:', Object.keys(messageData));
            console.log('[AI Run Relay] 🔍 NEW messageData.generate_action:', !!messageData.generate_action);
            console.log('[AI Run Relay] 🔍 NEW messageData.generate_action?.generated_action:', !!messageData.generate_action?.generated_action);
            console.log('[AI Run Relay] 🔍 NEW messageData.generated_action:', !!messageData.generated_action);
            console.log('[AI Run Relay] 🔍 NEW generatedAction found:', !!newGeneratedAction);
            console.log('[AI Run Relay] 🔍 NEW generatedAction full object:', JSON.stringify(newGeneratedAction, null, 2));

            if (newGeneratedAction) {
              const newActionMethod = newGeneratedAction.method || newGeneratedAction.action;
              console.log('[AI Run Relay] 🔍 NEW generatedAction.method:', newGeneratedAction.method);
              console.log('[AI Run Relay] 🔍 NEW generatedAction.action:', newGeneratedAction.action);
              console.log('[AI Run Relay] 🎯 NEW Action method determined:', newActionMethod);

              // If it's not a screenshot, forward to VM execute_action
              if (newActionMethod && newActionMethod !== 'get_screenshot') {
                console.log('[AI Run Relay] ✅ NEW action found, forwarding to VM execute_action...');

                // Use cached VM IP for the new action
                try {
                  if (!cachedVmIp) {
                    throw new Error('VM IP not available from cache');
                  }

                  console.log(`[AI Run Relay] 📍 Using cached VM IP: ${cachedVmIp}`);

                  // Execute the new action on VM
                  const vmUrl = `http://${cachedVmIp}:7889/execute_action`;
                  // VM expects: response.generated_action, so we need to wrap it in response
                  const vmPayload = {
                    selected_screen: 0,
                    full_screen_game_mode: 0,
                    response: {
                      generated_action: newGeneratedAction
                    },
                    run_instance_id: runInstanceId,
                    mode: 'local_vm'
                  };

                  console.log(`[AI Run Relay] 🔍 Using VM-compatible payload format`);
                  console.log(`[AI Run Relay] 🔍 VM payload:`, JSON.stringify(vmPayload, null, 2));

                  console.log(`[AI Run Relay] 🚀 NEW Action execution to VM: ${vmUrl}`);

                  const vmResponse = await fetch(vmUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(vmPayload),
                    signal: AbortSignal.timeout(30000)
                  });

                  if (vmResponse.ok) {
                    const vmResult = await vmResponse.json();

                    // Log VM result but hide base64 data
                    if (vmResult.dimensions) {
                      console.log(`[AI Run Relay] ✅ NEW VM ${newActionMethod} response: Screenshot captured (${vmResult.dimensions.width}x${vmResult.dimensions.height})`);
                    } else {
                      const logResult = { ...vmResult };
                      if (logResult.base64_image) {
                        logResult.base64_image = `[BASE64_IMAGE_${logResult.base64_image.length}_CHARS]`;
                      }
                      console.log(`[AI Run Relay] ✅ NEW VM ${newActionMethod} response:`, JSON.stringify(logResult, null, 2));
                    }
                  } else {
                    console.error(`[AI Run Relay] ❌ NEW VM action failed: ${vmResponse.status}`);
                  }

                } catch (error) {
                  console.error(`[AI Run Relay] ❌ Error executing NEW action on VM:`, error);
                }
              }
            }

          } else {
            console.warn(`[AI Run Relay] ⚠️ Failed to send VM result back to AI run: ${resultResponse.status}`);
          }
        } catch (error) {
          console.error(`[AI Run Relay] ❌ Error sending VM result back to AI run:`, error);
        }
      }
    } else {
      console.log(`[AI Run Relay] ⚠️ No generated_action found in backend response, skipping VM forwarding`);
      console.log(`[AI Run Relay] 📋 Response status:`, messageData.status);
      console.log(`[AI Run Relay] 📋 Has ai_run_response:`, !!messageData.ai_run_response);
      console.log(`[AI Run Relay] 📋 Has direct generated_action:`, !!messageData.generated_action);
      console.log(`[AI Run Relay] 📋 Backend response structure:`, JSON.stringify(messageData, null, 2));
    }

    console.log(`[AI Run Relay] ✅ Process complete (${Date.now() - startTime}ms total)`);
    console.log(`[AI Run Relay] 📤 Final response being returned to frontend:`);
    console.log(`[AI Run Relay] 📤 - success: true`);
    console.log(`[AI Run Relay] 📤 - response.status: ${messageData.status}`);
    console.log(`[AI Run Relay] 📤 - response has generate_action: ${!!messageData.generate_action}`);
    console.log(`[AI Run Relay] 📤 - response has generated_action: ${!!messageData.generated_action}`);
    if (messageData.generate_action) {
      console.log(`[AI Run Relay] 📤 - generate_action.method: ${messageData.generate_action.method}`);
      console.log(`[AI Run Relay] 📤 - generate_action.generated_action: ${!!messageData.generate_action.generated_action}`);
      if (messageData.generate_action.generated_action) {
        console.log(`[AI Run Relay] 📤 - generate_action.generated_action.action: ${messageData.generate_action.generated_action.action}`);
      }
    }
    if (messageData.generated_action) {
      console.log(`[AI Run Relay] 📤 - generated_action.method: ${messageData.generated_action.method}`);
    }

    return {
      success: true,
      response: messageData,
      message: 'Message sent successfully with relay'
    };

  } catch (error) {
    console.error(`[AI Run Relay] ❌ Message error after ${Date.now() - startTime}ms:`, error);
    return {
      success: false,
      error: (error as Error).message,
      message: 'Failed to send message with relay'
    };
  }
});

// Stop AI Run with relay
ipcMain.handle('ai-run-relay-stop', async (event, { runInstanceId, metadata }) => {
  try {
    console.log('[AI Run Relay] Stopping AI run:', runInstanceId);

    const backendUrl = process.env.BACKEND_API_URL || 'http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com:8888';
    const token = metadata?.token || '';

    const response = await fetch(`${backendUrl}/console/api/ai-run/runs/${runInstanceId}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to stop AI run: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('[AI Run Relay] AI run stopped:', data);

    return {
      success: true,
      response: data,
      message: 'AI run stopped successfully'
    };

  } catch (error) {
    console.error('[AI Run Relay] Stop error:', error);
    return {
      success: false,
      error: (error as Error).message,
      message: 'Failed to stop AI run'
    };
  }
});

// Pause AI Run with relay
ipcMain.handle('ai-run-relay-pause', async (event, { runInstanceId, metadata }) => {
  try {
    console.log('[AI Run Relay] Pausing AI run:', runInstanceId);

    const backendUrl = process.env.BACKEND_API_URL || 'http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com:8888';
    const token = metadata?.token || '';

    const response = await fetch(`${backendUrl}/console/api/ai-run/runs/${runInstanceId}/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to pause AI run: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('[AI Run Relay] AI run paused:', data);

    return {
      success: true,
      response: data,
      message: 'AI run paused successfully'
    };

  } catch (error) {
    console.error('[AI Run Relay] Pause error:', error);
    return {
      success: false,
      error: (error as Error).message,
      message: 'Failed to pause AI run'
    };
  }
});

// Resume AI Run with relay
ipcMain.handle('ai-run-relay-resume', async (event, { runInstanceId, metadata }) => {
  try {
    console.log('[AI Run Relay] Resuming AI run:', runInstanceId);

    const backendUrl = process.env.BACKEND_API_URL || 'http://ec2-44-234-43-86.us-west-2.compute.amazonaws.com:8888';
    const token = metadata?.token || '';

    const response = await fetch(`${backendUrl}/console/api/ai-run/runs/${runInstanceId}/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to resume AI run: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('[AI Run Relay] AI run resumed:', data);

    return {
      success: true,
      response: data,
      message: 'AI run resumed successfully'
    };

  } catch (error) {
    console.error('[AI Run Relay] Resume error:', error);
    return {
      success: false,
      error: (error as Error).message,
      message: 'Failed to resume AI run'
    };
  }
});
