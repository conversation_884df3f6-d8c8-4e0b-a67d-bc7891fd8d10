import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from 'renderer/components/ui/button';
// import { WindowControls } from 'renderer/components/window-controls'; // No longer direct import
// import { AppModeToggle } from 'renderer/components/app-mode-toggle'; // No longer direct import
import { TitleBar } from 'renderer/components/title-bar'; // Import TitleBar

export function VmSetupScreen() {
  const navigate = useNavigate();
  const [currentAppMode, setCurrentAppMode] = useState('VMs');
  const [hyperVStatus, setHyperVStatus] = useState('');
  const [vmStatus, setVmStatus] = useState('');
  const [testOutput, setTestOutput] = useState('');
  const [vmIpAddress, setVmIpAddress] = useState('');
  const [rdpCredentials, setRdpCredentials] = useState({
    username: 'UseIt',
    password: '123456'
  });
  const [rdpUrl, setRdpUrl] = useState('');
  const [vhdxPath, setVhdxPath] = useState('');
  const [vmName, setVmName] = useState('');
  const [vmMemory, setVmMemory] = useState(2);
  const [vmProcessors, setVmProcessors] = useState(2);

  const handleCreateVm = async () => {
    if (window.App && window.App.createVm) {
      try {
        setVmStatus('Starting VM creation process...');
        const result = await window.App.createVm();
        console.log('VM creation script executed:', result);
        setVmStatus(result);
      } catch (error) {
        console.error('Error creating VM:', error);
        setVmStatus(`Error creating VM: ${(error as Error).message}`);
      }
    } else {
      console.error('Electron API (window.App.createVm) not found.');
      alert('Error: VM Creation API not available.');
    }
  };

  const handleCreateVmFromVhdx = async () => {
    if (!vhdxPath) {
      alert('Please select a VHDX file first.');
      return;
    }

    if (window.App && window.App.createVmFromVhdx) {
      try {
        setVmStatus('Starting VM creation from VHDX...');
        const options = {
          vhdxPath,
          vmName: vmName || undefined,
          vmMemory: vmMemory * 1024 * 1024 * 1024, // Convert GB to bytes
          vmProcessors,
        };
        const result = await window.App.createVmFromVhdx(options);
        console.log('VM creation from VHDX script executed:', result);
        setVmStatus(result);
      } catch (error) {
        console.error('Error creating VM from VHDX:', error);
        setVmStatus(`Error creating VM from VHDX: ${(error as Error).message}`);
      }
    } else {
      console.error('Electron API (window.App.createVmFromVhdx) not found.');
      alert('Error: VM Creation from VHDX API not available.');
    }
  };

  const handleSelectVhdxFile = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.vhdx';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setVhdxPath(file.path);
      }
    };
    input.click();
  };

  const handleEnableHyperV = async () => {
    if (window.App && window.App.enableHyperV) {
      try {
        setHyperVStatus('Enabling Hyper-V...');
        const result = await window.App.enableHyperV();
        console.log('Hyper-V enabling script executed:', result);
        setHyperVStatus(`Hyper-V enabling process finished. Output: ${result}. Please restart your computer.`);
      } catch (error) {
        console.error('Error enabling Hyper-V:', error);
        setHyperVStatus(`Error enabling Hyper-V: ${(error as Error).message}`);
      }
    } else {
      console.error('Electron API (window.App.enableHyperV) not found.');
      alert('Error: Enable Hyper-V API not available.');
    }
  };

  const handleRepairWindowsComponents = async () => {
    if (window.App && window.App.repairWindowsComponents) {
      try {
        setHyperVStatus('Starting Windows Component Repair...');
        const result = await window.App.repairWindowsComponents();
        console.log('Windows Component Repair script executed:', result);
        setHyperVStatus(`Windows Component Repair: ${result}. Please restart after completion.`);
      } catch (error) {
        console.error('Error running Windows Component Repair:', error);
        setHyperVStatus(`Error running Windows Component Repair: ${(error as Error).message}`);
      }
    } else {
      console.error('Electron API (window.App.repairWindowsComponents) not found.');
      alert('Error: Windows Component Repair API not available.');
    }
  };

  const handleTestGetStatus = async () => {
    if (window.App && window.App.getVmStatus) {
      try {
        setTestOutput("Getting VM status...");
        const result = await window.App.getVmStatus('UseIt-Dev-VM');
        setTestOutput(`Get Status Result: ${result}`);
      } catch (error) {
        setTestOutput(`Error: ${(error as Error).message}`);
      }
    }
  };

  const handleTestStartVm = async () => {
    if (window.App && window.App.startVm) {
      try {
        setTestOutput("Attempting to start VM...");
        const result = await window.App.startVm('UseIt-Dev-VM');
        setTestOutput(`Start VM Result: ${result}`);
      } catch (error) {
        setTestOutput(`Error: ${(error as Error).message}`);
      }
    }
  };

  const handleTestStopVm = async () => {
    if (window.App && window.App.stopVm) {
      try {
        setTestOutput("Attempting to stop VM...");
        const result = await window.App.stopVm('UseIt-Dev-VM');
        setTestOutput(`Stop VM Result: ${result}`);
      } catch (error) {
        setTestOutput(`Error: ${(error as Error).message}`);
      }
    }
  };

  const handleTestGetIp = async () => {
    if (window.App && window.App.getVmIpAddress) {
      try {
        setTestOutput("Attempting to get VM IP address...");
        const result = await window.App.getVmIpAddress('UseIt-Dev-VM');
        setTestOutput(`Get VM IP Result: ${result}`);
        setVmIpAddress(result);
      } catch (error) {
        setTestOutput(`Error: ${(error as Error).message}`);
        setVmIpAddress('');
      }
    }
  };



  const handleGetRdpUrl = async () => {
    if (vmIpAddress) {
      try {
        setTestOutput('Getting RDP URL...');
        console.log('Attempting to get RDP URL for IP:', vmIpAddress);
        
        if (window.App && window.App.getRdpUrl) {
          const result = await window.App.getRdpUrl({ ip: vmIpAddress });
          
          console.log('getRdpUrl result:', result);
          
          if (result.success) {
            setRdpUrl(result.url);
            setTestOutput(`RDP URL: ${result.url}`);
          } else {
            setTestOutput(`Failed to get RDP URL: ${result.error}`);
          }
        } else {
          setTestOutput('RDP URL API not available');
        }
      } catch (error) {
        console.error('Error in handleGetRdpUrl:', error);
        setTestOutput(`Error getting RDP URL: ${(error as Error).message}`);
      }
    } else {
      alert('Please get the VM IP address first.');
    }
  };

  const handleOpenRdpView = async () => {
    if (vmIpAddress) {
      try {
        setTestOutput('Opening RDP View...');
        console.log('Attempting to get mstsc autoconnect URL for IP:', vmIpAddress);
        
        if (window.App && window.App.getMstscUrl) {
          const result = await window.App.getMstscUrl({
            ip: vmIpAddress,
            username: rdpCredentials.username,
            password: rdpCredentials.password
          });
          
          console.log('getMstscUrl result:', result);
          
          if (result.success) {
            // 在当前窗口中显示iframe
            const rdpContainer = document.getElementById('rdp-iframe-container');
            if (rdpContainer) {
              rdpContainer.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 1000; display: flex; flex-direction: column;">
                  <div style="background: #1f2937; color: white; padding: 16px; display: flex; justify-content: space-between; align-items: center;">
                    <h2>RDP Connection - ${vmIpAddress}</h2>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">关闭</button>
                  </div>
                  <iframe src="${result.url}" style="flex: 1; border: none; width: 100%; height: calc(100% - 64px);" title="RDP Connection"></iframe>
                </div>
              `;
              setTestOutput('RDP View opened successfully');
            } else {
              // 创建容器
              const container = document.createElement('div');
              container.id = 'rdp-iframe-container';
              document.body.appendChild(container);
              container.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 1000; display: flex; flex-direction: column;">
                  <div style="background: #1f2937; color: white; padding: 16px; display: flex; justify-content: space-between; align-items: center;">
                    <h2>RDP Connection - ${vmIpAddress}</h2>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">关闭</button>
                  </div>
                  <iframe src="${result.url}" style="flex: 1; border: none; width: 100%; height: calc(100% - 64px);" title="RDP Connection"></iframe>
                </div>
              `;
              setTestOutput('RDP View opened successfully');
            }
          } else {
            setTestOutput(`Failed to get mstsc URL: ${result.error}`);
          }
        } else {
          setTestOutput('mstsc URL API not available');
        }
      } catch (error) {
        console.error('Error in handleOpenRdpView:', error);
        setTestOutput(`Error opening RDP view: ${(error as Error).message}`);
      }
    } else {
      alert('Please get the VM IP address first.');
    }
  };

  const handleTestTcpConnection = async () => {
    if (vmIpAddress) {
      try {
        setTestOutput('Testing TCP connection...');
        console.log('Testing TCP connection for IP:', vmIpAddress);
        
        if (window.App && window.App.testTcpConnection) {
          const result = await window.App.testTcpConnection({
            ip: vmIpAddress,
            port: 3389
          });
          
          console.log('testTcpConnection result:', result);
          
          if (result.success) {
            setTestOutput(`✅ TCP连接测试成功! ${result.message}`);
          } else {
            setTestOutput(`❌ TCP连接测试失败: ${result.error}`);
          }
        } else {
          setTestOutput('TCP Test API not available');
        }
      } catch (error) {
        console.error('Error in handleTestTcpConnection:', error);
        setTestOutput(`Error testing TCP connection: ${(error as Error).message}`);
      }
    } else {
      alert('Please get the VM IP address first.');
    }
  };

  const handleTestRdpConnection = async () => {
    if (vmIpAddress) {
      try {
        setTestOutput('Testing RDP connection...');
        console.log('Testing RDP connection for IP:', vmIpAddress);
        
        if (window.App && window.App.testRdpConnection) {
          const result = await window.App.testRdpConnection({
            ip: vmIpAddress,
            username: rdpCredentials.username,
            password: rdpCredentials.password
          });
          
          console.log('testRdpConnection result:', result);
          
          if (result.success) {
            setTestOutput(`✅ RDP Test Successful! Events: ${result.events.join(', ')}`);
          } else {
            setTestOutput(`❌ RDP Test Failed: ${result.error}${result.events ? ` Events: ${result.events.join(', ')}` : ''}`);
          }
        } else {
          setTestOutput('RDP Test API not available');
        }
      } catch (error) {
        console.error('Error in handleTestRdpConnection:', error);
        setTestOutput(`Error testing RDP connection: ${(error as Error).message}`);
      }
    } else {
      alert('Please get the VM IP address first.');
    }
  };

  const handleModeChange = (mode: string) => {
    console.log('Selected mode:', mode);
    setCurrentAppMode(mode);
    if (mode === 'Workflows') {
      // Navigate to playground page
      navigate('/playground');
    }
  };

  // Determine title bar height for content padding
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const titleBarHeight = isMac ? '40px' : '32px';

  return (
    <div
      className="app-container window-frame flex flex-col h-screen text-foreground box-border font-sans relative"
      style={{
        margin: '0',
        padding: '0',
        border: 'none',
        outline: 'none',
        overflow: 'hidden'
      }}
    >
      {/* 简化的顶栏 */}
      <div
        style={{
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: '#222225', // playground的深灰色背景
          WebkitAppRegion: 'drag',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
        } as any}
      >
        {/* 左侧按钮组 */}
        <div style={{ display: 'flex', alignItems: 'center', paddingLeft: '12px', WebkitAppRegion: 'no-drag' } as any}>
          <button
            onClick={() => handleModeChange('VMs')}
            style={{
              background: 'transparent', // 移除背景色
              color: currentAppMode === 'VMs' ? '#f79009' : '#9ca3af', // playground的橙色高亮
              border: 'none',
              padding: '4px 12px',
              fontSize: '14px',
              cursor: 'pointer',
              borderRadius: '4px',
              marginRight: '8px',
              transition: 'all 0.2s ease',
              fontWeight: currentAppMode === 'VMs' ? '600' : '400' // 选中时加粗
            }}
            onMouseEnter={(e) => {
              if (currentAppMode !== 'VMs') {
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (currentAppMode !== 'VMs') {
                e.currentTarget.style.color = '#9ca3af';
              }
            }}
          >
            VMs
          </button>
          <button
            onClick={() => handleModeChange('Workflows')}
            style={{
              background: 'transparent', // 移除背景色
              color: currentAppMode === 'Workflows' ? '#f79009' : '#9ca3af', // playground的橙色高亮
              border: 'none',
              padding: '4px 12px',
              fontSize: '14px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'all 0.2s ease',
              fontWeight: currentAppMode === 'Workflows' ? '600' : '400' // 选中时加粗
            }}
            onMouseEnter={(e) => {
              if (currentAppMode !== 'Workflows') {
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (currentAppMode !== 'Workflows') {
                e.currentTarget.style.color = '#9ca3af';
              }
            }}
          >
            Workflows
          </button>
        </div>

        {/* 右侧窗口控制按钮 */}
        <div style={{ display: 'flex', height: '100%', WebkitAppRegion: 'no-drag' } as any}>
          <button
            style={{
              width: '46px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.1)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            onClick={() => window.App?.windowMinimize?.()}
            aria-label="Minimize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 6h8" stroke="#ffffff" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </button>
          <button
            style={{
              width: '46px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.1)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            onClick={() => window.App?.windowMaximize?.()}
            aria-label="Maximize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="2" width="8" height="8" stroke="#ffffff" strokeWidth="1.5" fill="none" strokeLinecap="round"/>
            </svg>
          </button>
          <button
            style={{
              width: '48px',
              height: '100%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '0',
              borderTopRightRadius: '16px',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#e81123';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={() => window.App?.windowClose?.()}
            aria-label="Close"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 3l6 6M3 9l6-6" stroke="#ffffff" strokeWidth="1.5" strokeLinecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      <main style={{ paddingTop: 32, height: '100%', display: 'flex', flexDirection: 'column', background: 'var(--background)' }}>
        {currentAppMode === 'VMs' && (
          <div className="flex-grow flex flex-col items-center justify-center p-5 w-full bg-background">
            <div className="p-10 bg-card text-card-foreground rounded-xl shadow-lg text-center max-w-2xl w-full">
              <h1 className="text-2xl mb-3 text-foreground">
                VM Setup
              </h1>
              <p className="text-base mb-8 text-muted-foreground">
                Create your virtual machine from an existing VHDX file.
              </p>

              {/* VHDX Creation Mode */}
              <div className="flex flex-col gap-4">
                  {/* VHDX File Selection */}
                  <div className="text-left">
                    <label className="block text-sm font-medium mb-2 text-foreground">
                      VHDX File Path:
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={vhdxPath}
                        onChange={(e) => setVhdxPath(e.target.value)}
                        placeholder="Select or enter VHDX file path..."
                        className="flex-1 px-3 py-2 bg-background border border-gray-600 rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-orange-500"
                      />
                      <Button onClick={handleSelectVhdxFile} variant="outline" size="sm">
                        Browse
                      </Button>
                    </div>
                  </div>

                  {/* VM Configuration */}
                  <div className="grid grid-cols-2 gap-4 text-left">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        VM Name (optional):
                      </label>
                      <input
                        type="text"
                        value={vmName}
                        onChange={(e) => setVmName(e.target.value)}
                        placeholder="UseIt-Dev-VM"
                        className="w-full px-3 py-2 bg-background border border-gray-600 rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-orange-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2 text-foreground">
                        Memory (GB):
                      </label>
                      <input
                        type="number"
                        value={vmMemory}
                        onChange={(e) => setVmMemory(parseInt(e.target.value) || 2)}
                        min="1"
                        max="32"
                        className="w-full px-3 py-2 bg-background border border-gray-600 rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-orange-500"
                      />
                    </div>
                  </div>

                  <div className="text-left">
                    <label className="block text-sm font-medium mb-2 text-foreground">
                      Processors:
                    </label>
                    <input
                      type="number"
                      value={vmProcessors}
                      onChange={(e) => setVmProcessors(parseInt(e.target.value) || 2)}
                      min="1"
                      max="8"
                      className="w-32 px-3 py-2 bg-background border border-gray-600 rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>

                  <Button onClick={handleCreateVmFromVhdx} size="lg" disabled={!vhdxPath}>
                    Create VM from VHDX
                  </Button>
                  <p className="text-sm text-muted-foreground">
                    Creates a VM from an existing VHDX file (exported from Hyper-V).
                  </p>
              </div>

              {/* Common Actions */}
              <div className="flex flex-col gap-4 mt-8 pt-6 border-t border-gray-600">
                <Button onClick={handleEnableHyperV} size="lg" variant="outline">
                  Enable VM (for Windows Home)
                </Button>
                <Button onClick={() => navigate('/debug')} size="lg" variant="secondary">
                  🔧 Debug Electron APIs
                </Button>
              </div>

              {vmStatus && (
                <p className="text-sm mt-4 text-green-500">{vmStatus}</p>
              )}
              {hyperVStatus && (
                <p className="text-sm mt-4 text-muted-foreground">{hyperVStatus}</p>
              )}

            </div>
          </div>
        )}

        {currentAppMode === 'Workflows' && (
          <div className="flex-grow w-full h-full">
            <iframe
              src="http://localhost:3001"
              title="Workflows"
              style={{ width: '100%', height: '100%', border: 'none' }}
              // sandbox="allow-scripts allow-same-origin allow-forms allow-popups" // Consider sandbox attributes for security
            />
          </div>
        )}
      </main>
      <div id="rdp-iframe-container"></div>
    </div>
  );
} 