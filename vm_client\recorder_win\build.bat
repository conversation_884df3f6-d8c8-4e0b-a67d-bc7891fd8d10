@echo off
echo ================================================
echo Recorder Build Script
echo ================================================

echo.
echo [1/6] Checking package manager...
where pnpm >nul 2>&1
if errorlevel 1 (
    echo pnpm not found, trying to install...
    npm install -g pnpm
    if errorlevel 1 (
        echo pnpm install failed, using npm instead
        set USE_NPM=1
        goto cleanup
    )
)

if not defined USE_NPM (
    echo Using pnpm
    pnpm --version
) else (
    echo Using npm
    npm --version
)

:cleanup
echo.
echo [2/6] Cleaning environment...
if exist node_modules rmdir /s /q node_modules
if exist pnpm-lock.yaml del pnpm-lock.yaml
if exist package-lock.json del package-lock.json
if exist yarn.lock del yarn.lock
if exist release rmdir /s /q release
if exist dist rmdir /s /q dist
echo Environment cleaned

echo.
echo [3/6] Clearing cache...
if defined USE_NPM (
    npm cache clean --force
) else (
    pnpm store prune 2>nul
    pnpm cache clean --force 2>nul
)
echo Cache cleared

echo.
echo [4/6] Installing dependencies...
if defined USE_NPM (
    npm install
    if errorlevel 1 npm install --force
) else (
    pnpm install
    if errorlevel 1 (
        echo pnpm install failed, trying npm...
        set USE_NPM=1
        npm install --force
    )
)

if errorlevel 1 (
    echo ERROR: Dependency installation failed
    echo Try running as administrator or check network connection
    pause
    exit /b 1
)

echo Dependencies installed

echo.
echo [5/6] Adding critical dependencies...
if defined USE_NPM (
    npm install es-errors ms bytes --save
) else (
    pnpm add es-errors ms bytes
)

echo Critical dependencies added

echo.
echo [6/6] Building application...

if defined USE_NPM (
    npm run build:win
) else (
    pnpm run build:win
)

if errorlevel 1 (
    echo Build failed, trying npm...
    npm run build:win
    if errorlevel 1 (
        echo ERROR: Build failed
        pause
        exit /b 1
    )
)

echo.
echo ================================================
echo BUILD SUCCESSFUL!
echo ================================================
echo.
echo Output: release/ directory
echo.
echo Test APIs:
echo   curl -X POST http://localhost:3000/showWindow
echo   curl -X POST http://localhost:3000/hideWindow
echo.
echo Or run: test_api.bat
echo.
pause
