import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

declare global {
  interface Window {
    App: typeof API
    electronAPI: typeof API
  }
}

const API = {
  sayHelloFromBridge: () => console.log('\nHello from bridgeAPI! 👋\n\n'),
  username: process.env.USER,
  createVm: () => ipcRenderer.invoke('create-vm'),
  enableHyperV: () => ipcRenderer.invoke('enable-hyper-v'),
  repairWindowsComponents: () => ipcRenderer.invoke('repair-windows-components'),
  getVmStatus: (vmName: string) => ipcRenderer.invoke('get-vm-status', vmName),
  startVm: (vmName: string) => ipcRenderer.invoke('start-vm', vmName),
  stopVm: (vmName: string) => ipcRenderer.invoke('stop-vm', vmName),
  getVmIpAddress: (vmName: string) => ipcRenderer.invoke('get-vm-ip', vmName),
  getVmDetails: (vmName: string) => ipcRenderer.invoke('getVmDetails', { vmName }),
  getMstscStatus: () => ipcRenderer.invoke('get-mstsc-status'),
  startRecording: () => ipcRenderer.invoke('start-recording'),
  stopRecording: () => ipcRenderer.invoke('stop-recording'),
  getRecordingStatus: () => ipcRenderer.invoke('get-recording-status'),
  uploadRecording: () => ipcRenderer.invoke('upload-recording'),
  testVideoExe: () => ipcRenderer.invoke('test-video-exe'),
  readRecordedFile: (filePath: string) => ipcRenderer.invoke('read-recorded-file', { filePath }),
  testRecorderApi: () => ipcRenderer.invoke('test-recorder-api'),
  diagnoseVmEnvironment: () => ipcRenderer.invoke('diagnose-vm-environment'),
  startRdpSession: (options: { ip: string; username?: string; password?: string }) => 
    ipcRenderer.invoke('start-rdp-session', options),
  stopRdpSession: (options: { ip: string }) => ipcRenderer.invoke('stop-rdp-session', options),
  openRdpView: (options: { ip: string; username: string; password: string }) => ipcRenderer.invoke('open-rdp-view', options),
  getRdpCredentials: (options: { ip: string }) => ipcRenderer.invoke('get-rdp-credentials', options),
  getRdpUrl: (options: { ip: string }) => ipcRenderer.invoke('get-rdp-url', options),
  getMstscUrl: (options: { ip: string; username?: string; password?: string; domain?: string; sessionId?: string }) =>
    ipcRenderer.invoke('get-mstsc-url', options),
  testTcpConnection: (options: { ip: string; port?: number }) => ipcRenderer.invoke('test-tcp-connection', options),
  // VM Client APIs - File transfer and command execution
  vmClientUploadFile: (options: { ip: string; fileData: string; filePath: string; filename: string }) =>
    ipcRenderer.invoke('vm-client-upload-file', options),
  vmClientExecuteCommand: (options: { ip: string; command: string; workingDir?: string; timeout?: number }) =>
    ipcRenderer.invoke('vm-client-execute-command', options),
  executeLocalVmAction: (options: { action: any; runInstanceId: string }) =>
    ipcRenderer.invoke('execute-local-vm-action', options),
  checkVmPort: (options: { vmName: string; port: number }) =>
    ipcRenderer.invoke('check-vm-port', options),
  getPersistentRdpSession: (options: { vmName: string; sessionIndex: number; ip: string; username: string; password: string }) =>
    ipcRenderer.invoke('get-persistent-rdp-session', options),
  getRdpSessionsStatus: () =>
    ipcRenderer.invoke('get-rdp-sessions-status'),
  testRdpConnection: (options: { ip: string; username: string; password: string }) => ipcRenderer.invoke('test-rdp-connection', options),
  onRdpBitmapUpdate: (callback: (event: any, bitmap: any) => void) => {
    ipcRenderer.on('rdp-bitmap-update', callback);
    return () => {
      ipcRenderer.removeListener('rdp-bitmap-update', callback);
    };
  },
  onRdpConnectionStatus: (callback: (event: any, status: any) => void) => {
    ipcRenderer.on('rdp-connection-status', callback);
    return () => {
      ipcRenderer.removeListener('rdp-connection-status', callback);
    };
  },
  navigateToUrl: (url: string) => ipcRenderer.invoke('navigate-to-url', url),
  windowMinimize: () => ipcRenderer.send('window-minimize'),
  windowMaximize: () => ipcRenderer.send('window-maximize'),
  windowClose: () => ipcRenderer.send('window-close'),

  // AI Run Relay APIs
  aiRunRelayStart: (options: { workflowId: string; targetPcUrl: string; message: string; metadata?: any }) =>
    ipcRenderer.invoke('ai-run-relay-start', options),
  aiRunRelayMessage: (options: { runInstanceId: string; message: string; metadata?: any }) =>
    ipcRenderer.invoke('ai-run-relay-message', options),
  aiRunRelayStop: (options: { runInstanceId: string; metadata?: any }) =>
    ipcRenderer.invoke('ai-run-relay-stop', options),
  aiRunRelayPause: (options: { runInstanceId: string; metadata?: any }) =>
    ipcRenderer.invoke('ai-run-relay-pause', options),
  aiRunRelayResume: (options: { runInstanceId: string; metadata?: any }) =>
    ipcRenderer.invoke('ai-run-relay-resume', options),

}

contextBridge.exposeInMainWorld('App', API)
contextBridge.exposeInMainWorld('electronAPI', API)
