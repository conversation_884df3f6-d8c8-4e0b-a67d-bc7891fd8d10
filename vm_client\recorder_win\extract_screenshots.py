#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
提取SRT文件中的base64编码截图并保存为JPEG图像
"""

import os
import re
import base64
import argparse
from io import BytesIO
from PIL import Image
from datetime import datetime

def parse_srt_timestamp(timestamp):
    """解析SRT时间戳为可读格式"""
    # 格式: HH:MM:SS,毫秒 -> HH-MM-SS_毫秒
    parts = timestamp.replace(',', '_').replace(':', '-')
    return parts

def extract_screenshots(srt_file, output_dir=None):
    """从SRT文件中提取base64编码的图像并保存为JPEG"""
    # 如果未指定输出目录，则在SRT文件同级创建一个
    if output_dir is None:
        base_name = os.path.splitext(os.path.basename(srt_file))[0]
        output_dir = os.path.join(os.path.dirname(srt_file), f"{base_name}_images")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"正在从 {srt_file} 提取图像到 {output_dir}")
    
    # 用于匹配base64编码的正则表达式
    base64_pattern = re.compile(r'x=(\d+),y=(\d+),base64:(.+)')
    
    # 读取SRT文件
    with open(srt_file, 'r') as f:
        content = f.read()
    
    # 将SRT文件拆分为块
    blocks = content.strip().split('\n\n')
    extracted_count = 0
    
    for block in blocks:
        lines = block.strip().split('\n')
        if len(lines) >= 3:
            # 第一行是序号
            index = lines[0]
            # 第二行是时间戳
            timestamp_line = lines[1]
            timestamps = timestamp_line.split(' --> ')
            if len(timestamps) >= 1:
                timestamp = timestamps[0]
                # 第三行包含base64编码
                content_line = lines[2]
                match = base64_pattern.match(content_line)
                if match:
                    x, y, base64_data = match.groups()
                    # 解码base64数据
                    try:
                        image_data = base64.b64decode(base64_data)
                        # 使用PIL打开图像
                        image = Image.open(BytesIO(image_data))
                        # 保存为JPEG
                        formatted_timestamp = parse_srt_timestamp(timestamp)
                        output_file = os.path.join(output_dir, f"{index}_{formatted_timestamp}_x{x}_y{y}.jpg")
                        image.save(output_file, "JPEG", quality=95)
                        extracted_count += 1
                        print(f"已保存图像 {index} 到 {output_file}")
                    except Exception as e:
                        print(f"无法处理图像 {index}：{e}")
    
    print(f"完成！共提取了 {extracted_count} 张图像。")
    return extracted_count

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='从SRT文件中提取base64编码的截图并保存为JPEG图像')
    parser.add_argument('srt_file', help='包含base64编码截图的SRT文件路径')
    parser.add_argument('-o', '--output-dir', help='保存提取图像的目录路径（可选）')
    args = parser.parse_args()
    
    # 提取截图
    extract_screenshots(args.srt_file, args.output_dir)

if __name__ == "__main__":
    main() 