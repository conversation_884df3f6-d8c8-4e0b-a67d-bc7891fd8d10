# RDP 剪贴板和打字支持配置

## 概述

本文档描述了如何在Local VM的RDP连接中启用剪贴板共享和增强的打字支持功能。

## 功能特性

### ✅ 已启用的功能

#### 剪贴板和设备重定向
- **剪贴板共享** (`enableClipboard: true`)
  - 支持在本地计算机和远程VM之间复制粘贴文本
  - 支持复制粘贴文件和图片
- **本地打印机** (`enableLocalPrinters: true`)
  - 远程VM可以访问本地打印机
- **智能卡支持** (`enableSmartCards: true`)
  - 支持智能卡设备重定向

#### 音频设置
- **音频播放** (`audioPlayMode: 'local'`)
  - 远程VM的音频在本地播放
- **音频捕获** (`audioCaptureMode: false`)
  - 不启用音频捕获以提高性能

#### 显示优化
- **字体平滑** (`fontSmoothing: true`)
  - 启用字体平滑以提高文本显示质量
- **桌面合成** (`desktopComposition: true`)
  - 启用Aero效果和桌面合成
- **主题支持** (`showThemes: true`)
  - 显示Windows主题
- **闪烁光标** (`showBlinkingCursor: true`)
  - 在输入控件中显示闪烁光标
- **壁纸优化** (`showWallpaper: false`)
  - 不显示壁纸以提高性能

## 技术实现

### 修改的文件

1. **类型定义** (`src/types/node-rdpjs-2.d.ts`)
   - 扩展了RdpClientConfig接口
   - 添加了剪贴板、音频、显示等配置选项

2. **mstsc.js服务器** (`src/main/mstsc/server/mstsc.js`)
   - 更新了RDP客户端创建配置
   - 启用了剪贴板和设备重定向功能

3. **主进程RDP客户端** (`src/main/index.ts`)
   - 更新了startRdpSession处理器的配置
   - 添加了增强的RDP连接选项

4. **RDP测试模块** (`src/main/rdp-test.ts`)
   - 更新了测试配置以包含新功能

### 配置参数详解

```typescript
const rdpConfig = {
  // 基础连接配置
  domain: '',
  userName: 'UseIt',
  password: '123456',
  enablePerf: true,
  autoLogin: true,
  screen: { width: 1024, height: 768 },
  locale: 'en',
  logLevel: 'INFO',
  
  // 剪贴板和设备重定向
  enableClipboard: true,        // 🔥 关键：启用剪贴板共享
  enableLocalPrinters: true,    // 启用本地打印机
  enableSmartCards: true,       // 启用智能卡
  
  // 音频设置
  audioPlayMode: 'local',       // 本地播放音频
  audioCaptureMode: false,      // 不捕获音频
  
  // 显示优化
  showWallpaper: false,         // 不显示壁纸（性能优化）
  fontSmoothing: true,          // 字体平滑
  desktopComposition: true,     // 桌面合成/Aero
  showThemes: true,             // 显示主题
  showBlinkingCursor: true      // 闪烁光标
};
```

## 使用方法

### 1. 剪贴板功能
- **复制文本**：在本地复制文本，可以在VM中粘贴
- **粘贴文本**：在VM中复制的文本，可以在本地粘贴
- **文件传输**：通过剪贴板复制粘贴文件（需要VM支持）

### 2. 打字体验
- **字体平滑**：文本显示更清晰
- **闪烁光标**：输入框中有明显的光标指示
- **主题支持**：保持Windows原生外观

### 3. 音频支持
- **系统声音**：VM中的系统声音会在本地播放
- **应用音频**：VM中运行的应用程序音频会传输到本地

## 兼容性

### 支持的功能
- ✅ 文本剪贴板共享
- ✅ 本地打印机访问
- ✅ 音频播放
- ✅ 字体平滑和主题
- ✅ 智能卡重定向

### 可选功能（未启用）
- ❌ 本地驱动器映射 (`enableDrives`)
- ❌ COM端口重定向 (`enableLocalCOMPorts`)
- ❌ 即插即用设备 (`enablePlugAndPlayDevices`)
- ❌ 音频捕获 (`audioCaptureMode`)

## 性能优化

为了确保最佳性能，以下设置已优化：
- 禁用壁纸显示
- 启用性能模式 (`enablePerf: true`)
- 优化的屏幕分辨率设置
- 合理的音频配置

## 故障排除

### 剪贴板不工作
1. 确认VM中的RDP服务支持剪贴板重定向
2. 检查VM的组策略设置
3. 重新连接RDP会话

### 打字体验问题
1. 确认键盘布局设置 (`locale: 'en'`)
2. 检查VM中的输入法设置
3. 验证字体平滑是否启用

### 音频问题
1. 确认本地音频设备正常工作
2. 检查VM中的音频服务状态
3. 验证音频播放模式设置

## 更新日志

- **2025-08-01**: 初始版本，添加剪贴板和打字支持
- 启用了完整的设备重定向功能
- 优化了显示和音频设置
- 更新了所有相关配置文件

---

*文档版本: v1.0*  
*状态: ✅ 已实现*  
*适用环境: Windows 10/11 Hyper-V + Node.js 18+ + Electron*
