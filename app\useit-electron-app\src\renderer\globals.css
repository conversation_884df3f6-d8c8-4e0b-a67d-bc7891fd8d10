@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

/* Electron Window Styling - Premium rounded corners and shadows */
html, body {
  border-radius: 16px;
  overflow: hidden;
  background: var(--background) !important;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100vw;
  height: 100vh;
  /* Ensure no transparent gaps */
  box-sizing: border-box;
}

/* Ensure root element has proper background */
#root {
  background: var(--background) !important;
  border-radius: 16px;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* Fill any potential gaps */
  position: relative;
}

/* Main app container with premium styling */
.app-container {
  border-radius: 16px;
  overflow: hidden;
  min-height: 100vh;
  background: var(--background) !important;
  position: relative;
  width: 100%;
  height: 100%;
}

/* Fill any gaps with background color */
.app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background);
  border-radius: 16px;
  z-index: -1;
}

/* Enhanced window frame with premium glass effect */
.window-frame {
  border-radius: 16px;
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.06) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.15);

  /* Premium multi-layered shadow system */
  box-shadow:
    /* Main depth shadow */
    0 40px 80px rgba(0, 0, 0, 0.2),
    /* Mid-range shadow for depth */
    0 20px 40px rgba(0, 0, 0, 0.15),
    /* Close shadow for definition */
    0 8px 24px rgba(0, 0, 0, 0.12),
    /* Subtle inner highlight */
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    /* Outer glow for premium feel */
    0 0 0 1px rgba(255, 255, 255, 0.05);

  /* Smooth transitions for interactions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effect for interactive elements */
.window-frame:hover {
  box-shadow:
    0 50px 100px rgba(0, 0, 0, 0.25),
    0 25px 50px rgba(0, 0, 0, 0.18),
    0 12px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

/* Premium texture overlay */
.window-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

/* Content area styling */
.window-frame > * {
  position: relative;
  z-index: 2;
}

/* Title bar styling for rounded corners */
.title-bar {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

/* Smooth scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Selection styling with orange highlight */
::selection {
  background: rgba(247, 144, 9, 0.3); /* Orange selection background */
  color: inherit;
}

/* Modern macOS-style auto-hiding scrollbar - 现代化macOS风格自动隐藏滚动条 */
/* 全局默认滚动条样式 - 统一使用细滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 2px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox - 使用thin模式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
}

/* 自动隐藏滚动条类 - macOS风格 */
.auto-hide-scrollbar {
  /* 使用overlay滚动条，不占用布局空间 */
  overflow: overlay;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
}

.auto-hide-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  /* 使用overlay模式，滚动条浮在内容上方 */
}

.auto-hide-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  /* 默认透明度很低 */
  opacity: 0;
  transition: opacity 0.3s ease, background 0.2s ease;
}

/* 滚动时显示滚动条 */
.auto-hide-scrollbar:hover::-webkit-scrollbar-thumb,
.auto-hide-scrollbar.scrolling::-webkit-scrollbar-thumb {
  opacity: 1;
  background: rgba(255, 255, 255, 0.15);
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 细滚动条变体 - 统一使用4px宽度 */
.thin-auto-hide-scrollbar {
  overflow: overlay;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
}

.thin-auto-hide-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.thin-auto-hide-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-auto-hide-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease, background 0.2s ease;
}

.thin-auto-hide-scrollbar:hover::-webkit-scrollbar-thumb,
.thin-auto-hide-scrollbar.scrolling::-webkit-scrollbar-thumb {
  opacity: 1;
  background: rgba(255, 255, 255, 0.15);
}

.thin-auto-hide-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 完全隐藏滚动条（用于特殊情况） */
.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 滚动条自动隐藏的JavaScript辅助样式 */
@media screen {
  /* 确保在所有屏幕上都使用overlay滚动条 */
  .auto-hide-scrollbar {
    overflow: auto;
  }

  /* 支持overlay的浏览器使用overlay */
  @supports (overflow: overlay) {
    .auto-hide-scrollbar {
      overflow: overlay;
    }
  }
}

/* Ensure all main content areas have proper background */
main, .main-content, .content-area {
  background: var(--background) !important;
}

/* Fix any remaining transparent gaps */
.flex-1, .flex-grow {
  background: var(--background);
}

/* Specific fixes for iframe containers */
iframe {
  border-radius: 0px !important; /* 移除圆角避免白线 */
  overflow: hidden;
  border: none !important;
  outline: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure proper background for all containers */
div[style*="height: 100%"],
div[style*="height: 100vh"],
div[class*="h-full"],
div[class*="h-screen"] {
  background: var(--background);
}

/* Updated to use dark theme as default to align with web frontend's new design scheme */
:root {
  /* Dark theme variables from web frontend */
  --color-components-input-bg-normal: #ffffff14;
  --color-components-input-text-placeholder: #c8ceda4d;
  --color-components-input-bg-hover: #ffffff08;
  --color-components-input-bg-active: #ffffff0d;
  --color-components-input-border-active: #747481;
  --color-components-input-border-destructive: #f97066;
  --color-components-input-text-filled: #f4f4f5;
  --color-components-input-bg-destructive: #ffffff03;
  --color-components-input-bg-disabled: #ffffff08;
  --color-components-input-text-disabled: #c8ceda4d;
  --color-components-input-text-filled-disabled: #c8ceda99;
  --color-components-input-border-hover: #3a3a40;
  --color-components-input-border-active-prompt-1: #36bffa;
  --color-components-input-border-active-prompt-2: #296dff;
  --color-components-kbd-bg-gray: #ffffff08;
  --color-components-kbd-bg-white: #ffffff1f;
  --color-components-tooltip-bg: #18181bf2;
  --color-components-button-primary-text: #fffffff2;
  --color-components-button-primary-bg: #155aef;
  --color-components-button-primary-border: #ffffff1f;
  --color-components-button-primary-bg-hover: #296dff;
  --color-components-button-primary-border-hover: #ffffff33;
  --color-components-button-primary-bg-disabled: #ffffff08;
  --color-components-button-primary-border-disabled: #ffffff14;
  --color-components-button-primary-text-disabled: #ffffff33;
  --color-components-button-secondary-text: #ffffffcc;
  --color-components-button-secondary-text-disabled: #ffffff33;
  --color-components-button-secondary-bg: #ffffff1f;
  --color-components-button-secondary-bg-hover: #ffffff33;
  --color-components-button-secondary-bg-disabled: #ffffff08;
  --color-components-button-secondary-border: #ffffff14;
  --color-components-button-secondary-border-hover: #ffffff1f;
  --color-components-button-secondary-border-disabled: #ffffff0d;
  --color-components-panel-bg: #222225;
  --color-components-panel-border: #c8ceda24;

  /* Orange highlight colors from web frontend */
  --color-orange-primary: #f79009;
  --color-orange-soft: #f7900933;
  --color-orange-dark: #ff4405;

  /* Mapping to shadcn/ui general variables - Dark theme as default */
  --background: #1d1d20; /* Dark background from web frontend */
  --foreground: #fbfbfc; /* Light text from web frontend */
  --card: var(--color-components-panel-bg, #222225);
  --card-foreground: var(--color-components-input-text-filled, #f4f4f5);
  --popover: var(--color-components-panel-bg, #222225);
  --popover-foreground: var(--color-components-input-text-filled, #f4f4f5);
  --primary: var(--color-components-button-primary-bg, #155aef);
  --primary-foreground: var(--color-components-button-primary-text, #fffffff2);
  --secondary: var(--color-components-button-secondary-bg, #ffffff1f);
  --secondary-foreground: var(--color-components-button-secondary-text, #ffffffcc);
  --muted: var(--color-components-input-bg-normal, #ffffff14);
  --muted-foreground: var(--color-components-input-text-placeholder, #c8ceda4d);
  --accent: var(--color-orange-primary, #f79009); /* Orange highlight color */
  --accent-foreground: #ffffff;
  --destructive: #d92d20;
  --destructive-foreground: #fffffff2;
  --border: var(--color-components-input-border-active, #747481);
  --input: var(--color-components-input-border-active, #747481);
  --ring: var(--color-orange-primary, #f79009); /* Orange focus ring */
  --radius: 0.5rem;
}

/* Light Theme Variables (optional fallback - app now defaults to dark) */
.light {
  --color-components-input-bg-normal: #c8ceda40;
  --color-components-input-text-placeholder: #98a2b2;
  --color-components-input-bg-hover: #c8ceda24;
  --color-components-input-bg-active: #f9fafb;
  --color-components-input-border-active: #d0d5dc;
  --color-components-input-border-destructive: #fda29b;
  --color-components-input-text-filled: #101828;
  --color-components-input-bg-destructive: #ffffff;
  --color-components-input-bg-disabled: #c8ceda24;
  --color-components-input-text-disabled: #d0d5dc;
  --color-components-input-text-filled-disabled: #676f83;
  --color-components-input-border-hover: #d0d5dc;
  --color-components-input-border-active-prompt-1: #0ba5ec;
  --color-components-input-border-active-prompt-2: #155aef;
  --color-components-kbd-bg-gray: #1018280a;
  --color-components-kbd-bg-white: #ffffff1f;
  --color-components-tooltip-bg: #fffffff2;
  --color-components-button-primary-text: #ffffff;
  --color-components-button-primary-bg: #155aef;
  --color-components-button-primary-border: #1018280a;
  --color-components-button-primary-bg-hover: #004aeb;
  --color-components-button-primary-border-hover: #10182814;
  --color-components-button-primary-bg-disabled: #155aef24;
  --color-components-button-primary-border-disabled: #ffffff00;
  --color-components-button-primary-text-disabled: #ffffff99;
  --color-components-button-secondary-text: #354052;
  --color-components-button-secondary-text-disabled: #10182840;
  --color-components-button-secondary-bg: #ffffff;
  --color-components-button-secondary-bg-hover: #f9fafb;
  --color-components-button-secondary-bg-disabled: #f9fafb;
  --color-components-button-secondary-border: #10182824;
  --color-components-button-secondary-border-hover: #10182833;
  --color-components-button-secondary-border-disabled: #1018280a;
  --color-components-panel-bg: #ffffff;
  --color-components-panel-border: #10182814;

  /* Light theme mapping */
  --background: #f2f4f7;
  --foreground: var(--color-components-input-text-filled, #101828);
  --card: var(--color-components-panel-bg, #ffffff);
  --card-foreground: var(--color-components-input-text-filled, #101828);
  --popover: var(--color-components-panel-bg, #ffffff);
  --popover-foreground: var(--color-components-input-text-filled, #101828);
  --primary: var(--color-components-button-primary-bg, #155aef);
  --primary-foreground: var(--color-components-button-primary-text, #ffffff);
  --secondary: var(--color-components-button-secondary-bg, #ffffff);
  --secondary-foreground: var(--color-components-button-secondary-text, #354052);
  --muted: var(--color-components-input-bg-normal, #c8ceda40);
  --muted-foreground: var(--color-components-input-text-placeholder, #98a2b2);
  --accent: var(--color-orange-primary, #f79009); /* Orange highlight color */
  --accent-foreground: #ffffff;
  --destructive: #d92d20;
  --destructive-foreground: #ffffff;
  --border: var(--color-components-input-border-active, #d0d5dc);
  --input: var(--color-components-input-border-active, #d0d5dc);
  --ring: var(--color-orange-primary, #f79009); /* Orange focus ring */
}

@theme inline {
  /* This section maps the above CSS variables to Tailwind's theme.
     Updated to include orange colors and align with web frontend's dark theme.
  */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Orange color theme from web frontend */
  --color-orange-primary: var(--color-orange-primary);
  --color-orange-soft: var(--color-orange-soft);
  --color-orange-dark: var(--color-orange-dark);

  /* Radius values */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50; /* Uses --border and --ring now */
    @apply antialiased;
  }

  body {
    @apply bg-background text-foreground;
    min-height: 100vh;
    overflow: hidden; /* Ensures content respects the border-radius */
    border-radius: 16px; /* Match the window border radius */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Example shadow, adjust as needed */
  }

  html,
  body {
    user-select: none;
    scroll-behavior: smooth;
    -webkit-app-region: no-drag;
    -webkit-font-smoothing: antialiased;
    /* Explicitly set height to 100% for html and body */
    height: 100%;
    margin: 0;
    padding: 0;
  }

  /* Allow text selection and editing in input fields and textareas */
  input,
  textarea,
  [contenteditable="true"] {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Ensure input fields are focusable and editable */
  input:not([disabled]):not([readonly]),
  textarea:not([disabled]):not([readonly]) {
    pointer-events: auto !important;
    cursor: text !important;
  }
}

/* Orange highlight utility classes */
@layer utilities {
  .text-orange-primary {
    color: var(--color-orange-primary);
  }

  .bg-orange-primary {
    background-color: var(--color-orange-primary);
  }

  .bg-orange-soft {
    background-color: var(--color-orange-soft);
  }

  .border-orange-primary {
    border-color: var(--color-orange-primary);
  }

  .ring-orange-primary {
    --tw-ring-color: var(--color-orange-primary);
  }

  .focus\:ring-orange-primary:focus {
    --tw-ring-color: var(--color-orange-primary);
  }

  .hover\:bg-orange-soft:hover {
    background-color: var(--color-orange-soft);
  }

  .hover\:text-orange-primary:hover {
    color: var(--color-orange-primary);
  }
}
