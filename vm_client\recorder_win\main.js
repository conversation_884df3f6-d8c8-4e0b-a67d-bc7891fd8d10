const { app, BrowserWindow, ipcMain, systemPreferences, Tray, Menu, screen } = require('electron');
const { desktopCapturer } = require('electron');
const express = require('express');
const bodyParser = require('body-parser');
const apiApp = express();
const cors = require('cors');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const os = require('os');

let mainWindow = null;
let pythonProcess;
let tray = null;

// 添加录制状态变量
let isRecording = false;
let recordedFilePath = null;

// 设置Express中间件
apiApp.use(cors());
apiApp.use(bodyParser.json());
apiApp.use(bodyParser.urlencoded({ extended: true }));

// 创建托盘图标
function createTray() {
    // 创建原生图像对象
    const nativeImage = require('electron').nativeImage;
    const icon = nativeImage.createFromPath(path.join(__dirname, 'assets', 'icon.png'));
    
    // 调整图标大小为 22x22 像素（macOS 推荐尺寸）
    const resizedIcon = icon.resize({
        width: 22,
        height: 22
    });
    
    // 使用调整后的图标创建托盘
    tray = new Tray(resizedIcon);
    tray.setToolTip('Screen Recorder');
    
    // 创建上下文菜单
    const contextMenu = Menu.buildFromTemplate([
        { 
            label: 'Show/Hide',
            click: () => toggleWindow() 
        },
        { type: 'separator' },
        { 
            label: 'Exit', 
            click: () => {
                app.isQuitting = true;
                if (pythonProcess) {
                    pythonProcess.kill();
                }
                app.quit();
            } 
        }
    ]);
    
    // 设置托盘的上下文菜单
    tray.setContextMenu(contextMenu);
    
    // 点击托盘图标时显示/隐藏窗口
    tray.on('click', () => {
        toggleWindow();
    });
}

// 获取窗口位置
function getWindowPosition() {
    const windowBounds = mainWindow.getBounds();
    const trayBounds = tray.getBounds();
    const screen = require('electron').screen;
    const primaryDisplay = screen.getPrimaryDisplay();
    const screenBounds = primaryDisplay.bounds;
    
    // 计算窗口位置，确保不会超出屏幕边界
    let x = Math.round(trayBounds.x + (trayBounds.width / 2) - (windowBounds.width / 2));
    let y = Math.round(trayBounds.y + trayBounds.height + 4);
    
    // 确保窗口不会超出屏幕右边界
    if (x + windowBounds.width > screenBounds.width) {
        x = screenBounds.width - windowBounds.width;
    }
    
    // 确保窗口不会超出屏幕左边界
    if (x < 0) {
        x = 0;
    }
    
    // 如果窗口会超出屏幕底部，则将其显示在托盘图标上方
    if (y + windowBounds.height > screenBounds.height) {
        y = trayBounds.y - windowBounds.height - 4;
    }
    
    // macOS 特殊处理：考虑菜单栏高度
    // if (process.platform === 'darwin') {
    //     y += 22; // macOS 菜单栏的大致高度
    // }
    
    return { x, y };
}

function toggleWindow() {
    if (mainWindow.isVisible()) {
        mainWindow.hide();
    } else {
        showWindow();
    }
}

function showWindow() {
    const position = getWindowPosition();
    mainWindow.setPosition(position.x, position.y, false);
    mainWindow.show();
    mainWindow.focus();
}

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 300,
        height: 400,
        show: false,
        frame: false,
        resizable: false,
        alwaysOnTop: true,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: true,
            allowRunningInsecureContent: false
        }
    });

    if (process.platform === 'darwin') {
        const hasScreenCapturePermission = systemPreferences.getMediaAccessStatus('screen');
        if (hasScreenCapturePermission !== 'granted') {
            systemPreferences.askForMediaAccess('screen')
                .then(granted => {
                    if (!granted) {
                        console.log('Screen recording permission denied');
                    }
                });
        }
    }

    mainWindow.loadFile('index.html');
    
    // 修改窗口关闭事件处理
    mainWindow.on('close', (event) => {
        console.log('Window close event triggered');  // 添加日志
        console.log('isQuitting:', app.isQuitting);  // 添加日志
        
        if (!app.isQuitting) {
            event.preventDefault();
            if (isRecording) {
                console.log('Recording in progress, stopping...');  // 添加日志
                mainWindow.webContents.send('stop-recording');
            } else {
                console.log('Hiding window');  // 添加日志
                mainWindow.hide();
            }
        } else {
            console.log('Quitting application');  // 添加日志
        }
    });

    // 失去焦点时隐藏窗口
    mainWindow.on('blur', () => {
        if (!mainWindow.webContents.isDevToolsOpened()) {
            mainWindow.hide();
        }
    });

    mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
        if (permission === 'media') {
            callback(true);
        } else {
            callback(false);
        }
    });
}

// 修改应用启动逻辑
app.whenReady().then(() => {
    // 隐藏dock图标（仅macOS）
    if (process.platform === 'darwin') {
        app.dock.hide();
    }
    startPythonServer();
    createWindow();
    createTray();
});

// 修改退出逻辑
app.on('before-quit', () => {
    console.log('Before quit event triggered');  // 添加日志
    app.isQuitting = true;
    if (pythonProcess) {
        pythonProcess.kill();
    }
});

// API路由
apiApp.post('/startRecording', async (req, res) => {
    console.log("Received request body:", req.body);
    console.log("Title value:", req.body.title);
    if (!mainWindow) {
        return res.json({
            status: 'error',
            message: 'Application window is not ready'
        });
    }

    if (isRecording) {
        return res.json({
            status: 'error',
            message: 'Recording is already in progress'
        });
    }

    try {
        // 如果没有指定sourceId，则自动获取主屏幕
        let sourceId = req.body.sourceId;
        const customTitle = req.body.title || "Recording";
        if (!sourceId) {
            const sources = await desktopCapturer.getSources({
                types: ['screen']
            });
            
            // 查找主屏幕
            const primarySource = sources.find(source => 
                source.name.includes('主') || 
                source.name.includes('Primary') || 
                source.name.includes('Display 1')
            );
            
            // 如果找到主屏幕就使用，否则使用第一个可用屏幕
            sourceId = primarySource ? primarySource.id : sources[0]?.id;
            
            if (!sourceId) {
                return res.json({
                    status: 'error',
                    message: 'No available screen source found'
                });
            }
        }

        // Send start command to renderer
        mainWindow.webContents.send('start-recording', sourceId, customTitle);

        // Wait for the recording to actually start
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Start recording timeout'));
            }, 10000); // 10 second timeout

            // Listen for recording started event
            ipcMain.once('recording-started', () => {
                clearTimeout(timeout);
                isRecording = true;
                resolve();
            });

            // Listen for recording error event
            ipcMain.once('recording-error', (event, errorMessage) => {
                clearTimeout(timeout);
                reject(new Error(errorMessage));
            });
        });

        res.json({
            status: 'success',
            message: 'Recording started successfully'
        });
    } catch (error) {
        console.error('Error starting recording:', error);
        res.json({
            status: 'error',
            message: 'Failed to start recording'
        });
    }
});

apiApp.post('/stopRecording', async (req, res) => {
    if (!mainWindow) {
        return res.json({
            status: 'error',
            message: 'Application window is not ready'
        });
    }

    if (!isRecording) {
        return res.json({
            status: 'error',
            message: 'No recording in progress'
        });
    }

    try {
        // Send stop command to renderer
        mainWindow.webContents.send('stop-recording');

        // Wait for the recording to actually stop and file to be saved
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Stop recording timeout'));
            }, 30000); // 30 second timeout

            // Listen for recording stopped event
            ipcMain.once('recording-stopped', () => {
                clearTimeout(timeout);
                resolve();
            });

            // Listen for recording error event
            ipcMain.once('recording-error', (event, errorMessage) => {
                clearTimeout(timeout);
                reject(new Error(errorMessage));
            });
        });

        res.json({
            status: 'success',
            message: 'Recording stopped and saved successfully'
        });
    } catch (error) {
        console.error('Error stopping recording:', error);
        res.json({
            status: 'error',
            message: 'Failed to stop recording: ' + error.message
        });
    }
});

apiApp.get('/getRecordedFilePath', (req, res) => {
    if (!recordedFilePath) {
        return res.json({
            status: 'error',
            message: 'File not available'
        });
    }

    res.json({
        status: 'success',
        filePath: recordedFilePath
    });
});

apiApp.get('/getSources', async (req, res) => {
    try {
        const sources = await desktopCapturer.getSources({
            types: ['screen'],
            thumbnailSize: {
                width: 150,
                height: 80
            }
        });

        const displays = screen.getAllDisplays();
        
        // 根据坐标(0,0)判断主显示器
        const primaryDisplay = displays.find(d => d.bounds.x === 0 && d.bounds.y === 0);
        if (!primaryDisplay) {
            console.error('Cannot find primary display at (0,0)');
            return;
        }

        // 将显示器按照主显示器优先的顺序排序
        const sortedDisplays = [...displays].sort((a, b) => {
            const aIsPrimary = a.bounds.x === 0 && a.bounds.y === 0;
            const bIsPrimary = b.bounds.x === 0 && b.bounds.y === 0;
            return bIsPrimary - aIsPrimary;
        });

        const sourcesWithInfo = sources.map((source, index) => {
            const display = sortedDisplays[index];
            const isPrimary = display.bounds.x === 0 && display.bounds.y === 0;
            
            return {
                id: source.id,
                name: `Screen ${index + 1}${isPrimary ? ' (main monitor)' : ''}`,
                thumbnail: source.thumbnail.toDataURL(),
                isPrimary: isPrimary,
                bounds: {
                    x: display.bounds.x,
                    y: display.bounds.y,
                    width: display.bounds.width,
                    height: display.bounds.height
                },
                workArea: display.workArea,
                scaleFactor: display.scaleFactor,
                displayId: display.id
            };
        });
        
        res.json({
            status: 'success',
            sources: sourcesWithInfo
        });
    } catch (error) {
        console.error('Error getting sources:', error);
        res.json({
            status: 'error',
            message: 'Failed to get screen sources'
        });
    }
});

apiApp.get('/getStatus', async (req, res) => {
    if (!mainWindow) {
        return res.json({
            status: 'error',
            message: 'Application window is not ready'
        });
    }

    try {
        // 向渲染进程请求录制时间
        mainWindow.webContents.send('get-recording-time');

        // 等待渲染进程返回录制时间
        const time = await new Promise((resolve) => {
            ipcMain.once('recording-time-response', (event, time) => {
                resolve(time);
            });
        });

        if (isRecording) {
            res.json({
                status: 'success',
                message: `Recording: ${time}s`
            });
        } else {
            res.json({
                status: 'success',
                message: 'Stopped'
            });
        }
    } catch (error) {
        console.error('Error getting status:', error);
        res.json({
            status: 'error',
            message: 'Failed to get recording status'
        });
    }
});

// 显示窗口接口
apiApp.post('/showWindow', (req, res) => {
    if (!mainWindow) {
        return res.json({
            status: 'error',
            message: 'Application window is not ready'
        });
    }

    try {
        showWindow();
        res.json({
            status: 'success',
            message: 'Window shown successfully'
        });
    } catch (error) {
        console.error('Error showing window:', error);
        res.json({
            status: 'error',
            message: 'Failed to show window'
        });
    }
});

// 隐藏窗口到托盘接口
apiApp.post('/hideWindow', (req, res) => {
    if (!mainWindow) {
        return res.json({
            status: 'error',
            message: 'Application window is not ready'
        });
    }

    try {
        mainWindow.hide();
        res.json({
            status: 'success',
            message: 'Window hidden to tray successfully'
        });
    } catch (error) {
        console.error('Error hiding window:', error);
        res.json({
            status: 'error',
            message: 'Failed to hide window'
        });
    }
});

// 刷新屏幕源接口
apiApp.post('/refreshSources', async (req, res) => {
    try {
        const sources = await desktopCapturer.getSources({
            types: ['screen'],
            thumbnailSize: {
                width: 150,
                height: 80
            }
        });

        const displays = screen.getAllDisplays();

        // 根据坐标(0,0)判断主显示器
        const primaryDisplay = displays.find(d => d.bounds.x === 0 && d.bounds.y === 0);
        if (!primaryDisplay) {
            console.error('Cannot find primary display at (0,0)');
            return res.json({
                status: 'error',
                message: 'Cannot find primary display'
            });
        }

        // 将显示器按照主显示器优先的顺序排序
        const sortedDisplays = [...displays].sort((a, b) => {
            const aIsPrimary = a.bounds.x === 0 && a.bounds.y === 0;
            const bIsPrimary = b.bounds.x === 0 && b.bounds.y === 0;
            return bIsPrimary - aIsPrimary;
        });

        const sourcesWithInfo = sources.map((source, index) => {
            const display = sortedDisplays[index];
            const isPrimary = display.bounds.x === 0 && display.bounds.y === 0;

            return {
                id: source.id,
                name: `Screen ${index + 1}${isPrimary ? ' (main monitor)' : ''}`,
                thumbnail: source.thumbnail.toDataURL(),
                isPrimary: isPrimary,
                bounds: {
                    x: display.bounds.x,
                    y: display.bounds.y,
                    width: display.bounds.width,
                    height: display.bounds.height
                },
                workArea: display.workArea,
                scaleFactor: display.scaleFactor,
                displayId: display.id
            };
        });

        res.json({
            status: 'success',
            message: 'Sources refreshed successfully',
            sources: sourcesWithInfo
        });
    } catch (error) {
        console.error('Error refreshing sources:', error);
        res.json({
            status: 'error',
            message: 'Failed to refresh screen sources'
        });
    }
});

// 获取最新录制文件路径接口
apiApp.get('/getLatestRecording', (req, res) => {
    try {
        const savePath = path.join(os.homedir(), 'Downloads', 'record_save');

        // 检查保存目录是否存在
        if (!fs.existsSync(savePath)) {
            return res.json({
                status: 'error',
                message: 'Recording directory does not exist'
            });
        }

        // 获取目录中所有视频文件
        const files = fs.readdirSync(savePath)
            .filter(file => file.endsWith('.mkv') || file.endsWith('.mp4'))
            .map(file => {
                const fullPath = path.join(savePath, file);
                const stats = fs.statSync(fullPath);
                return {
                    path: fullPath,
                    filename: file,
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime
                };
            })
            .sort((a, b) => b.modified - a.modified); // 按修改时间降序排列

        if (files.length === 0) {
            return res.json({
                status: 'error',
                message: 'No recordings found'
            });
        }

        const latestFile = files[0];
        res.json({
            status: 'success',
            message: 'Latest recording found',
            latestRecording: {
                path: latestFile.path,
                filename: latestFile.filename,
                size: latestFile.size,
                sizeFormatted: formatFileSize(latestFile.size),
                created: latestFile.created.toISOString(),
                modified: latestFile.modified.toISOString()
            },
            allRecordings: files.map(file => ({
                path: file.path,
                filename: file.filename,
                size: file.size,
                sizeFormatted: formatFileSize(file.size),
                created: file.created.toISOString(),
                modified: file.modified.toISOString()
            }))
        });
    } catch (error) {
        console.error('Error getting latest recording:', error);
        res.json({
            status: 'error',
            message: 'Failed to get latest recording'
        });
    }
});

// 文件传输接口 - 对外接口
apiApp.post('/uploadFile', async (req, res) => {
    try {
        // 转发请求到内部Python服务
        const http = require('http');

        const postData = JSON.stringify(req.body);

        const options = {
            hostname: '127.0.0.1',
            port: 4000,
            path: '/uploadFile',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 30000
        };

        const proxyReq = http.request(options, (proxyRes) => {
            let data = '';

            proxyRes.on('data', (chunk) => {
                data += chunk;
            });

            proxyRes.on('end', () => {
                try {
                    const responseData = JSON.parse(data);
                    res.status(proxyRes.statusCode).json(responseData);
                } catch (parseError) {
                    res.status(500).json({
                        status: 'error',
                        message: 'Invalid response from internal service'
                    });
                }
            });
        });

        proxyReq.on('error', (error) => {
            console.error('Error forwarding upload request:', error);

            if (error.code === 'ECONNREFUSED') {
                res.status(503).json({
                    status: 'error',
                    message: 'Internal service unavailable'
                });
            } else {
                res.status(500).json({
                    status: 'error',
                    message: 'Failed to process upload request: ' + error.message
                });
            }
        });

        proxyReq.on('timeout', () => {
            proxyReq.destroy();
            res.status(408).json({
                status: 'error',
                message: 'Request timeout'
            });
        });

        proxyReq.write(postData);
        proxyReq.end();

    } catch (error) {
        console.error('Error processing upload request:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to process upload request: ' + error.message
        });
    }
});

// 命令执行接口 - 对外接口
apiApp.post('/executeCommand', async (req, res) => {
    console.log('Received executeCommand request:', req.body);
    try {
        // 转发请求到内部Python服务
        const http = require('http');

        const postData = JSON.stringify(req.body);

        const options = {
            hostname: '127.0.0.1',
            port: 4000,
            path: '/executeCommand',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 60000 // 60秒超时，因为命令执行可能需要更长时间
        };

        const proxyReq = http.request(options, (proxyRes) => {
            let data = '';

            proxyRes.on('data', (chunk) => {
                data += chunk;
            });

            proxyRes.on('end', () => {
                try {
                    const responseData = JSON.parse(data);
                    res.status(proxyRes.statusCode).json(responseData);
                } catch (parseError) {
                    res.status(500).json({
                        status: 'error',
                        message: 'Invalid response from internal service'
                    });
                }
            });
        });

        proxyReq.on('error', (error) => {
            console.error('Error forwarding command request:', error);
            console.error('Error code:', error.code);
            console.error('Error message:', error.message);

            if (error.code === 'ECONNREFUSED') {
                res.status(503).json({
                    status: 'error',
                    message: 'Internal service unavailable - connection refused'
                });
            } else {
                res.status(500).json({
                    status: 'error',
                    message: 'Failed to process command request: ' + error.message + ' (code: ' + error.code + ')'
                });
            }
        });

        proxyReq.on('timeout', () => {
            proxyReq.destroy();
            res.status(408).json({
                status: 'error',
                message: 'Request timeout'
            });
        });

        proxyReq.write(postData);
        proxyReq.end();

    } catch (error) {
        console.error('Error processing command request:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to process command request: ' + error.message
        });
    }
});

// 辅助函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// IPC事件处理
ipcMain.on('recording-started', () => {
    isRecording = true;
});

ipcMain.on('recording-stopped', () => {
    isRecording = false;
});

ipcMain.on('recording-saved', (event, filePath) => {
    recordedFilePath = filePath;
});

ipcMain.on('recording-error', (event, errorMessage) => {
    isRecording = false;
    recordedFilePath = null;
});

// 添加到 IPC 事件处理部分
ipcMain.on('hide-window', () => {
    if (mainWindow) {
        mainWindow.hide();
    }
});

// 添加到 IPC 事件处理部分
ipcMain.on('close-window', () => {
    console.log('Received close-window event');  // 添加日志
    
    if (mainWindow) {
        if (isRecording) {
            console.log('Recording in progress, stopping before quit');  // 添加日志
            mainWindow.webContents.send('stop-recording');
            ipcMain.once('recording-stopped', () => {
                console.log('Recording stopped, now quitting');  // 添加日志
                app.isQuitting = true;
                if (pythonProcess) {
                    pythonProcess.kill();
                }
                mainWindow.destroy();  // 使用 destroy 而不是 quit
            });
        } else {
            console.log('No recording in progress, quitting directly');  // 添加日志
            app.isQuitting = true;
            if (pythonProcess) {
                pythonProcess.kill();
            }
            mainWindow.destroy();  // 使用 destroy 而不是 quit
        }
    }
});

// 启动API服务器
const apiServer = apiApp.listen(3000, () => {
    console.log('API server is running on port 3000');
});

function startPythonServer() {
    let executablePath;
    
    if (app.isPackaged) {
        // 打包后的路径
        executablePath = path.join(process.resourcesPath, 'InputListener.exe');
    } else {
        // 开发环境的路径
        executablePath = path.join(__dirname, 'InputListener.exe');
    }
  
    console.log('Executable path:', executablePath);
  
    // 检查可执行文件是否存在
    if (!fs.existsSync(executablePath)) {
        console.error('Executable not found at:', executablePath);
        console.error('请确保已经使用 PyInstaller 编译 listener.py 并将文件复制到正确位置');
        return;
    }
  
    // Windows 下不需要设置执行权限
    try {
        pythonProcess = spawn(executablePath, [], {
            stdio: 'pipe',
            shell: false,
            windowsHide: true  // 防止显示命令行窗口
        });
  
        pythonProcess.stdout.on('data', (data) => {
            console.log(`Listener输出: ${data.toString()}`);
        });
  
        pythonProcess.on('error', (error) => {
            console.error('启动Listener进程失败:', error);
        });
  
        pythonProcess.on('close', (code) => {
            console.log(`Listener进程退出, 退出码: ${code}`);
            pythonProcess = null;
        });
  
    } catch (error) {
        console.error('启动Listener服务时出错:', error);
    }
}

// 添加 IPC 处理程序（在创建窗口函数之前）
ipcMain.handle('get-app-path', () => {
    return {
        isPackaged: app.isPackaged,
        resourcesPath: process.resourcesPath,
        appPath: app.getAppPath()
    };
});

// 添加处理程序来获取保存路径
ipcMain.handle('get-save-path', () => {
    // 无论是打包应用还是开发环境，都使用Downloads/record_save
    const savePath = path.join(os.homedir(), 'Downloads', 'record_save');
    
    // 确保目录存在
    if (!fs.existsSync(savePath)) {
        try {
            fs.mkdirSync(savePath, { recursive: true });
            console.log('已创建保存目录:', savePath);
        } catch (err) {
            console.error('创建保存目录失败:', err);
        }
    }
    
    console.log('使用保存路径:', savePath);
    return savePath;
});

// 修改 IPC 处理器
ipcMain.handle('get-sources', async () => {
    try {
        const sources = await desktopCapturer.getSources({
            types: ['screen'],
            thumbnailSize: {
                width: 150,
                height: 80
            }
        });
        
        const displays = screen.getAllDisplays();
        
        // 根据坐标(0,0)判断主显示器
        const primaryDisplay = displays.find(d => d.bounds.x === 0 && d.bounds.y === 0);
        if (!primaryDisplay) {
            console.error('Cannot find primary display at (0,0)');
            return [];
        }

        // 将显示器按照主显示器优先的顺序排序
        const sortedDisplays = [...displays].sort((a, b) => {
            const aIsPrimary = a.bounds.x === 0 && a.bounds.y === 0;
            const bIsPrimary = b.bounds.x === 0 && b.bounds.y === 0;
            return bIsPrimary - aIsPrimary;
        });
        
        return sources.map((source, index) => {
            const display = sortedDisplays[index];
            const isPrimary = display.bounds.x === 0 && display.bounds.y === 0;
            
            return {
                id: source.id,
                name: `Screen ${index}`,
                thumbnail: source.thumbnail.toDataURL(),
                isPrimary: isPrimary,
                bounds: {
                    x: display.bounds.x,
                    y: display.bounds.y,
                    width: display.bounds.width,
                    height: display.bounds.height
                },
                workArea: display.workArea,
                scaleFactor: display.scaleFactor,
                displayId: display.id
            };
        });
    } catch (error) {
        console.error('Error getting sources:', error);
        throw error;
    }
});

app.on('window-all-closed', () => {
    console.log('All windows closed event triggered');  // 添加日志
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

ipcMain.handle('get-display-bounds', (event, sourceId) => {
    const displays = screen.getAllDisplays();
    
    // 根据坐标(0,0)判断主显示器
    const primaryDisplay = displays.find(d => d.bounds.x === 0 && d.bounds.y === 0);
    if (!primaryDisplay) {
        console.error('Cannot find primary display at (0,0)');
        return null;
    }

    // 将显示器按照主显示器优先的顺序排序
    const sortedDisplays = [...displays].sort((a, b) => {
        const aIsPrimary = a.bounds.x === 0 && a.bounds.y === 0;
        const bIsPrimary = b.bounds.x === 0 && b.bounds.y === 0;
        return bIsPrimary - aIsPrimary;
    });
    
    // 从 sourceId 中提取显示器索引
    const displayIndex = parseInt(sourceId.split(':')[1]);
    const display = sortedDisplays[displayIndex];
    
    if (display) {
        return {
            bounds: {
                x: display.bounds.x,
                y: display.bounds.y,
                width: display.bounds.width,
                height: display.bounds.height
            },
            workArea: display.workArea,
            scaleFactor: display.scaleFactor,
            isPrimary: display.bounds.x === 0 && display.bounds.y === 0,
            displayNumber: displayIndex + 1
        };
    }
    return null;
});

ipcMain.handle('get-display-info', (event, sourceId) => {
    try {
        console.log(`Getting display info for sourceId: ${sourceId}`);
        const displays = screen.getAllDisplays();
        
        // Try to extract display index from sourceId (format is typically "screen:X")
        let displayIndex = -1;
        if (sourceId && sourceId.includes(':')) {
            const parts = sourceId.split(':');
            if (parts.length > 1) {
                displayIndex = parseInt(parts[1]);
            }
        }
        
        // Sort displays with primary first
        const sortedDisplays = [...displays].sort((a, b) => {
            const aIsPrimary = a.bounds.x === 0 && a.bounds.y === 0;
            const bIsPrimary = b.bounds.x === 0 && b.bounds.y === 0;
            return bIsPrimary - aIsPrimary;
        });
        
        // Find the target display
        let targetDisplay = null;
        
        // First try to get by index
        if (displayIndex >= 0 && displayIndex < sortedDisplays.length) {
            targetDisplay = sortedDisplays[displayIndex];
        } 
        // Fall back to primary display
        else {
            targetDisplay = displays.find(d => d.bounds.x === 0 && d.bounds.y === 0) || displays[0];
        }
        
        if (targetDisplay) {
            return {
                scaleFactor: targetDisplay.scaleFactor,
                bounds: targetDisplay.bounds,
                workArea: targetDisplay.workArea,
                id: targetDisplay.id,
                isPrimary: targetDisplay.bounds.x === 0 && targetDisplay.bounds.y === 0
            };
        } else {
            console.error('No display found for sourceId:', sourceId);
            return {
                scaleFactor: 1.0,
                bounds: null
            };
        }
    } catch (error) {
        console.error('Error in get-display-info:', error);
        return {
            scaleFactor: 1.0,
            bounds: null,
            error: error.message
        };
    }
});

// 添加处理程序来获取所有显示器信息
ipcMain.handle('get-all-displays', () => {
    return screen.getAllDisplays();
});