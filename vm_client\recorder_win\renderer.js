const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const ffmpeg = require('fluent-ffmpeg');
let ffmpegPath;
let ffprobePath;

ipcRenderer.invoke('get-app-path').then(({ isPackaged, resourcesPath }) => {
    if (isPackaged) {
        // In packaged app, use the executables copied to the resources folder
        ffmpegPath = path.join(process.resourcesPath, 'ffmpeg.exe');
        ffprobePath = path.join(process.resourcesPath, 'ffprobe.exe');
    } else {
        // In development, use the node modules
        ffmpegPath = require('ffmpeg-static');
        ffprobePath = require('ffprobe-static').path;
    }
    
    console.log('Using FFmpeg at:', ffmpegPath);
    console.log('Using FFprobe at:', ffprobePath);
    
    ffmpeg.setFfmpegPath(ffmpegPath);
    ffmpeg.setFfprobePath(ffprobePath);
});

let savePath;
let ffmpegCommand = null;
let recordStartTime;
let recordedFilePath = null;
let selectedSourceId = null;
let selectedDisplayName = null;
let timeUpdateInterval;
let inputLogFilePath = null;
let screenshotSrtFilePath = null;

// Initialize DOM element references as null
let sourcesList = null;
let recordingIndicator = null;
let recordingTime = null;
let statusDisplay = null;
let filePathDisplay = null;
let startBtn = null;
let stopBtn = null;

// Function to initialize UI elements
function initializeUIElements() {
    sourcesList = document.getElementById('sourcesList');
    recordingIndicator = document.getElementById('recordingIndicator');
    recordingTime = document.getElementById('recordingTime');
    statusDisplay = document.getElementById('status');
    filePathDisplay = document.getElementById('filePath');
    startBtn = document.getElementById('startBtn');
    stopBtn = document.getElementById('stopBtn');
}

// When DOM is loaded, initialize UI elements
document.addEventListener('DOMContentLoaded', initializeUIElements);

// First get the save path
ipcRenderer.invoke('get-save-path').then(path => {
    savePath = path;
    console.log('Save path:', savePath);
});

// Then get ffmpeg and ffprobe paths
ipcRenderer.invoke('get-app-path').then(({ isPackaged, resourcesPath }) => {
    if (isPackaged) {
        ffmpegPath = path.join(process.resourcesPath, 'ffmpeg');
        ffprobePath = path.join(process.resourcesPath, 'ffprobe');
    } else {
        ffmpegPath = ffmpegStatic;
        ffprobePath = ffprobeStatic;
    }

    console.log('FFmpeg path:', ffmpegPath);
    console.log('FFprobe path:', ffprobePath);

    ffmpeg.setFfmpegPath(ffmpegPath);
    ffmpeg.setFfprobePath(ffprobePath);
});

function updateStatus(message, isError = false) {
    // Check if statusDisplay exists before updating it
    if (statusDisplay) {
        statusDisplay.textContent = message;
        statusDisplay.className = isError ? 'error' : 'success';
    } else {
        console.log(`Status update (${isError ? 'error' : 'info'}): ${message}`);
    }
}

async function getSources() {
    try {
        const sources = await ipcRenderer.invoke('get-sources');
        sourcesList.innerHTML = '';
        
        sources.forEach(source => {
            const sourceElement = document.createElement('div');
            sourceElement.className = 'source-item';
            sourceElement.style.cssText = `
                border: 2px solid ${source.isPrimary ? '#4CAF50' : '#ccc'};
                padding: 10px;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;
            `;
            
            sourceElement.innerHTML = `
                <img src="${source.thumbnail}" style="width: 100%; height: auto; margin-bottom: 8px;">
                <div style="text-align: center;">
                    ${source.name}
                </div>
            `;
            
            sourceElement.onclick = () => {
                document.querySelectorAll('.source-item').forEach(item => {
                    item.style.borderColor = '#ccc';
                });
                sourceElement.style.borderColor = '#4CAF50';
                selectedSourceId = source.id;
                selectedDisplayName = source.name;
                startBtn.disabled = false;
            };
            
            sourcesList.appendChild(sourceElement);
        });

        if (sources.length > 0) {
            selectedSourceId = sources[0].id;
            selectedDisplayName = sources[0].name;
            startBtn.disabled = false;
            startBtn.className = 'btn btn-default';
            stopBtn.disabled = true;
            stopBtn.className = 'btn btn-default';
            document.querySelector('.source-item').style.borderColor = '#4CAF50';
        }
    } catch (error) {
        updateStatus('Failed to get recording sources: ' + error.message, true);
    }
}

function updateRecordingTime() {
    if (recordStartTime && ffmpegCommand) {
        const seconds = Math.floor((Date.now() - recordStartTime) / 1000);
        // Make sure recordingTime element exists before updating
        const recordingTimeElement = document.getElementById('recordingTime');
        if (recordingTimeElement) {
            recordingTimeElement.textContent = seconds;
        }
    }
}

function startTimeUpdate() {
    timeUpdateInterval = setInterval(updateRecordingTime, 1000);
}

function stopTimeUpdate() {
    if (timeUpdateInterval) {
        clearInterval(timeUpdateInterval);
        timeUpdateInterval = null;
    }
}

async function getRecordedFilePath() {
    // Check if filePathDisplay exists before updating it
    if (filePathDisplay) {
        if (recordedFilePath) {
            filePathDisplay.textContent = 'File path: ' + recordedFilePath;
        } else {
            filePathDisplay.textContent = 'No recorded file';
        }
    } else {
        console.log('File path display:', recordedFilePath || 'No recorded file');
    }
}

async function startRecording(customTitle) {
    recordedFilePath = null;

    if (!selectedSourceId) {
        updateStatus('Please select a recording source first', true);
        return;
    }

    try {
        // Make sure savePath is initialized
        if (!savePath) {
            savePath = await ipcRenderer.invoke('get-save-path');
        }

        // Check if save path exists and is writable
        try {
            // Test write access by creating a temp file
            const testFile = path.join(savePath, '.test_write_access');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);
        } catch (err) {
            // Continue even if there's an error
        }

        // 1. Get the display bounds
        const displayBounds = await ipcRenderer.invoke('get-display-bounds', selectedSourceId);
        let width = 2560; // Default value
        let height = 1440; // Default value
        
        if (displayBounds && displayBounds.bounds) {
            width = displayBounds.bounds.width;
            height = displayBounds.bounds.height;
        }
        
        // Calculate target dimensions based on 1920px width
        const aspectRatio = width / height;
        const targetWidth = 1920;
        const targetHeight = Math.round(targetWidth / aspectRatio);

        const singaporeTime = new Date(new Date().getTime() + (8 * 60 * 60 * 1000));
        const timestamp = singaporeTime.toISOString()
            .replace(/T/, '_')
            .replace(/\..+/, '')
            .replace(/:/g, '-');
        
        // Extract time part from timestamp (hour-minute-second)
        const timePart = timestamp.split('_')[1];
        
        // Use custom title if provided, otherwise use the screen name
        let filePrefix;
        if (customTitle && typeof customTitle === 'string') {
            // Sanitize the custom title to ensure it's safe for filenames
            filePrefix = customTitle.replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
        } else {
            // Fall back to using the screen name if no valid custom title is provided
            filePrefix = selectedDisplayName.replace(/\s+/g, '-');
        }
        
        const fileName = `${filePrefix}-${timePart}.mkv`;
        recordedFilePath = path.join(savePath, fileName);
        
        // Build FFmpeg command array for logging with scaling filter
        const ffmpegArgs = [
            '-f', 'gdigrab',
            '-draw_mouse', '1',            // Show mouse cursor in recording
            '-framerate', '30',
            '-i', 'desktop',
            '-vf', `scale=${targetWidth}:${targetHeight}`,  // Add scaling filter
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-b:v', '10000k',              // Increased bitrate for higher resolution 
            '-metadata', `title=${filePrefix}`,
            '-metadata', `screen_name=${selectedDisplayName}`,
            '-metadata', `recording_time=${timestamp}`,
            recordedFilePath
        ];
        
        // Create FFmpeg process with stdin redirect
        const ffmpegProc = require('child_process').spawn(ffmpegPath, ffmpegArgs, {
            stdio: ['pipe', 'pipe', 'pipe'] // Redirect stdin, stdout, stderr
        });
        
        // Store the process
        ffmpegCommand = ffmpegProc;
        
        // Handle stdout, stderr
        let stdoutData = '';
        let stderrData = '';
        
        ffmpegProc.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                stdoutData += output + '\n';
            }
        });
        
        ffmpegProc.stderr.on('data', (data) => {
            // FFmpeg outputs progress to stderr
            const output = data.toString().trim();
            if (output) {
                stderrData += output + '\n';
            }
        });
        
        ffmpegProc.on('error', (err) => {
            updateStatus('Recording error: ' + err.message, true);
            ipcRenderer.send('recording-error', err.message);
        });
        
        ffmpegProc.on('exit', (code, signal) => {
            // Only update status if it was an unexpected exit (not triggered by our stop method)
            if (ffmpegCommand) {
                updateStatus(`Recording stopped unexpectedly (code: ${code})`, true);
                ffmpegCommand = null;
            }
        });

        recordStartTime = Date.now();
        
        // Update UI
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.className = 'btn btn-primary';
        }
        
        if (stopBtn) {
            stopBtn.disabled = false;
            stopBtn.className = 'btn btn-default';
        }
        
        updateStatus('Recording started');
        startTimeUpdate();
        
        // Start input recording service
        try {
            await startInputRecording();
        } catch (inputErr) {
            // Continue with screen recording only
        }
        
        ipcRenderer.send('recording-started');
    } catch (e) {
        let errorMessage = 'Recording failed: ';
        if (e.name === 'NotAllowedError') {
            errorMessage += 'Please allow screen recording permission in system preferences';
        } else if (e.name === 'NotReadableError') {
            errorMessage += 'Unable to access screen content';
        } else {
            errorMessage += e.message;
        }
        
        updateStatus(errorMessage, true);
        ipcRenderer.send('recording-error', e.message);
    }
}
async function stopRecording() {
    if (ffmpegCommand) {
        try {
            // Store a reference to the command before nullifying it
            const tempCommand = ffmpegCommand;
            
            // IMPORTANT: Set to null IMMEDIATELY to prevent "unexpected exit" message
            ffmpegCommand = null;
            
            // First ensure buttons exist to avoid null reference errors
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            
            // Try to gracefully stop FFmpeg
            try {
                tempCommand.stdin.write('q');
            } catch (stdinErr) {
                // Will attempt to stop process through other means
            }
            
            // Wait a bit for FFmpeg to finish up
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Check if the process has already exited
            const processAlreadyExited = !tempCommand || tempCommand.exitCode !== null;
            
            if (!processAlreadyExited) {
                // Wait for process to fully end or timeout after 5 seconds
                await new Promise(resolve => {
                    const timeout = setTimeout(() => {
                        // If FFmpeg is still running after timeout, try to kill it
                        if (tempCommand && tempCommand.exitCode === null) {
                            try {
                                tempCommand.kill('SIGKILL');
                            } catch (killErr) {
                                // Error killing FFmpeg process
                            }
                        }
                        
                        resolve();
                    }, 5000);
                    
                    // Only attach exit listener if process hasn't exited yet
                    tempCommand.on('exit', (code, signal) => {
                        clearTimeout(timeout);
                        resolve();
                    });
                });
            }
            
            // Set command to null so we know it's being stopped intentionally
            ffmpegCommand = null;
            
            // Get data from input recording
            try {
                const inputRecordingData = await stopInputRecording();
            } catch (inputErr) {
                // Continuing with FFmpeg shutdown
            }
            
            // Wait for process to fully end or timeout after 5 seconds
            await new Promise(resolve => {
                const timeout = setTimeout(() => {
                    // If FFmpeg is still running after timeout, try to kill it
                    if (tempCommand && tempCommand.exitCode === null) {
                        try {
                            tempCommand.kill('SIGKILL');
                        } catch (killErr) {
                            // Error killing FFmpeg process
                        }
                    }
                    
                    resolve();
                }, 5000);
                
                tempCommand.on('exit', (code, signal) => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
            
            // Check if the output file was created
            try {
                const stats = fs.statSync(recordedFilePath);
                const fileSizeMB = stats.size / (1024 * 1024);
            } catch (fileErr) {
                // Output file not found or not accessible
            }
            
            // After FFmpeg has stopped, we need to attach input logs if they exist
            if (inputLogFilePath && fs.existsSync(inputLogFilePath)) {
                try {
                    // Store the original path for possible reattachment
                    const originalPath = recordedFilePath;
                    
                    await attachInputLogs(recordedFilePath, inputLogFilePath, screenshotSrtFilePath);
                    
                    // Verify if the file path has changed during attachInputLogs
                    if (!fs.existsSync(recordedFilePath)) {
                        // Try to find the processed file by checking for a similar name
                        const dir = path.dirname(originalPath);
                        const baseFileName = path.basename(originalPath, path.extname(originalPath));
                        const files = fs.readdirSync(dir);
                        
                        // Look for files with similar names (e.g. might have temp extensions)
                        const possibleMatches = files.filter(file => 
                            file.startsWith(baseFileName) || 
                            file.includes(baseFileName)
                        );
                        
                        if (possibleMatches.length > 0) {
                            // Use the most recently modified file
                            const mostRecentFile = possibleMatches.map(filename => {
                                const fullPath = path.join(dir, filename);
                                return {
                                    path: fullPath,
                                    mtime: fs.statSync(fullPath).mtime
                                };
                            }).sort((a, b) => b.mtime - a.mtime)[0].path;
                        }
                    }
                } catch (attachErr) {
                    // Error attaching input logs
                }
            }
            
            // Verify that recordedFilePath points to an existing file
            try {
                const stats = fs.statSync(recordedFilePath);
            } catch (err) {
                // Try to locate the file in the save directory
                const saveDir = path.dirname(recordedFilePath);
                if (fs.existsSync(saveDir)) {
                    try {
                        const files = fs.readdirSync(saveDir)
                            .filter(file => file.endsWith('.mkv') || file.endsWith('.mp4'))
                            .map(file => {
                                const fullPath = path.join(saveDir, file);
                                return {
                                    path: fullPath,
                                    mtime: fs.statSync(fullPath).mtime
                                };
                            })
                            .sort((a, b) => b.mtime - a.mtime); // Sort by most recent
                    } catch (e) {
                        // Error scanning directory
                    }
                }
            }
            
            // Update UI elements if they exist
            if (startBtn) {
                startBtn.disabled = false;
                startBtn.className = 'btn btn-default';
            }
            
            if (stopBtn) {
                stopBtn.disabled = true;
                stopBtn.className = 'btn btn-default';
            }
            
            updateStatus('Recording stopped and saved');
            stopTimeUpdate();

            // Update file path display AFTER all processing
            getRecordedFilePath();

            // IMPORTANT: Send IPC events AFTER attachInputLogs completes
            // This ensures the file path includes the embedded action trace
            ipcRenderer.send('recording-stopped');
            ipcRenderer.send('recording-saved', recordedFilePath);

            // Update file path display
            getRecordedFilePath();
        } catch (error) {
            updateStatus('Failed to stop recording: ' + error.message, true);
        }
    }
}

async function startInputRecording() {
    try {
        try {
            // First try a simple GET request to check if the service is running
            const pingResponse = await fetch('http://localhost:4000/', {
                method: 'GET',
                timeout: 1000 // 1 second timeout
            }).catch(error => {
                throw new Error(`Connection failed: ${error.message}`);
            });
        } catch (pingError) {
            // Input recording service not accessible
        }
        
        const response = await fetch('http://localhost:4000/startRecording', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 3000 // 3 second timeout
        });
        
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || 'Unknown error');
        }
        
        return data;
    } catch (error) {
        throw error;
    }
}

async function stopInputRecording() {
    try {
        const response = await fetch('http://localhost:4000/stopRecording', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 3000 // 3 second timeout
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            if (data.filename) {
                inputLogFilePath = data.filename;
            }
            
            // Save screenshot SRT file path
            if (data.screenshot_srt) {
                screenshotSrtFilePath = data.screenshot_srt;
            }
            
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        throw error;
    }
}

async function attachInputLogs(videoPath, logPath, srtPath) {
    return new Promise((resolve, reject) => {
        const processingDialog = document.getElementById('processingDialog');
        
        // Check if processingDialog exists before using it
        if (processingDialog) {
            processingDialog.showModal();
            processingDialog.innerHTML = '<div class="dialog-body"><p>Attaching input logs...</p></div>';
        }

        // Create a temporary output file
        const tempOutputPath = videoPath + '.temp.mkv';
        
        // Basic output options
        const outputOptions = [
            '-c', 'copy',  // Copy all streams without re-encoding
            // Add input log file as attachment
            '-attach', logPath,
            '-metadata:s:t:0', 'mimetype=text/plain',
            '-metadata:s:t:0', `filename=${path.basename(logPath)}`,
        ];
        
        // Add screenshot SRT file as attachment (if exists)
        if (srtPath && fs.existsSync(srtPath)) {
            outputOptions.push('-attach', srtPath);
            outputOptions.push('-metadata:s:t:1', 'mimetype=text/plain');
            outputOptions.push('-metadata:s:t:1', `filename=${path.basename(srtPath)}`);
        }
        
        ffmpeg()
            .input(videoPath)
            .outputOptions(outputOptions)
            .output(tempOutputPath)
            .on('progress', (progress) => {
                if (progress.percent) {
                    const percent = Math.round(progress.percent);
                    
                    if (processingDialog) {
                        processingDialog.innerHTML = `<div class="dialog-body">
                            <p>Attaching input logs: ${percent}%</p>
                        </div>`;
                    }
                }
            })
            .on('end', () => {
                try {
                    // Replace original file with the new one
                    fs.unlinkSync(videoPath);
                    fs.renameSync(tempOutputPath, videoPath);
                    
                    // Clean up input log files
                    fs.unlinkSync(logPath);
                    inputLogFilePath = null;
                    
                    if (srtPath && fs.existsSync(srtPath)) {
                        fs.unlinkSync(srtPath);
                        screenshotSrtFilePath = null;
                    }
                    
                    // Close dialog if it exists
                    if (processingDialog) {
                        processingDialog.close();
                    }
                    
                    resolve();
                } catch (err) {
                    reject(err);
                }
            })
            .on('error', (err) => {
                // Close dialog if it exists
                if (processingDialog) {
                    processingDialog.close();
                }
                reject(err);
            })
            .run();
    });
}

function getRecordingTime() {
    if (!recordStartTime || !ffmpegCommand) {
        return 0;
    }
    return Math.floor((Date.now() - recordStartTime) / 1000);
}

ipcRenderer.on('get-recording-time', (event) => {
    const time = getRecordingTime();
    ipcRenderer.send('recording-time-response', time);
});

ipcRenderer.on('get-recorded-file-path', (event) => {
    ipcRenderer.send('recorded-file-path-response', recordedFilePath);
});

ipcRenderer.on('start-recording', async (event, sourceId, customTitle) => {
    try {
        selectedSourceId = sourceId;
        // Find the display name
        const sources = await ipcRenderer.invoke('get-sources');
        const selectedSource = sources.find(source => source.id === sourceId);
        if (selectedSource) {
            selectedDisplayName = selectedSource.name;
        } else {
            selectedDisplayName = 'Screen-' + sourceId;
        }
        
        // Pass the custom title to startRecording function
        startRecording(customTitle);
    } catch (e) {
        console.error('Recording failed:', e);
        alert('Recording failed, please make sure screen recording permission is granted');
        ipcRenderer.send('recording-error', e.message);
    }
});

ipcRenderer.on('stop-recording', () => {
    stopRecording();
});

// Put all initialization and event binding in DOMContentLoaded event
document.addEventListener('DOMContentLoaded', () => {
    // Initialize UI elements
    initializeUIElements();

    // Get all button elements
    const refreshSourcesBtn = document.getElementById('refreshSourcesBtn');
    const getPathBtn = document.getElementById('getPathBtn');
    const closeBtn = document.getElementById('closeBtn');

    // Make sure all elements exist
    if (refreshSourcesBtn) {
        refreshSourcesBtn.addEventListener('click', getSources);
    } else {
        console.error('refreshSourcesBtn element not found');
    }

    if (startBtn) {
        // Call startRecording without any parameters when clicked from the UI
        startBtn.addEventListener('click', () => startRecording("Recording"));
    }else {
        console.error('startBtn element not found');
    }

    if (stopBtn) {
        stopBtn.addEventListener('click', stopRecording);
    } else {
        console.error('stopBtn element not found');
    }

    if (getPathBtn) {
        getPathBtn.addEventListener('click', getRecordedFilePath);
    } else {
        console.error('getPathBtn element not found');
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            if (ffmpegProcess) {
                if (confirm('Recording is not finished, are you sure you want to exit?')) {
                    stopRecording().then(() => {
                        ipcRenderer.send('close-window');
                    });
                }
            } else {
                ipcRenderer.send('close-window');
            }
        });
    } else {
        console.error('closeBtn element not found');
    }
    
    // Initialize to get media sources
    getSources();
});