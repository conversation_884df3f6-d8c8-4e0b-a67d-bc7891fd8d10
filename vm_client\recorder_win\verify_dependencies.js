#!/usr/bin/env node

/**
 * 验证脚本：检查所有必需的依赖是否正确安装
 */

console.log('Verifying Recorder dependencies...\n');

const requiredDependencies = [
    'electron',
    'express',
    'body-parser',
    'cors',
    'depd',
    'fluent-ffmpeg',
    'tmp',
    'accepts',
    'array-flatten',
    'content-disposition',
    'content-type',
    'cookie',
    'cookie-signature',
    'debug',
    'encodeurl',
    'escape-html',
    'etag',
    'finalhandler',
    'fresh',
    'http-errors',
    'merge-descriptors',
    'methods',
    'on-finished',
    'parseurl',
    'path-to-regexp',
    'proxy-addr',
    'qs',
    'range-parser',
    'safe-buffer',
    'send',
    'serve-static',
    'setprototypeof',
    'statuses',
    'type-is',
    'utils-merge',
    'vary',
    'ms',
    'bytes',
    'unpipe',
    'destroy',
    'raw-body',
    'iconv-lite',
    'media-typer',
    'mime-types',
    'mime-db',
    'negotiator',
    'forwarded',
    'ipaddr.js',
    'ee-first',
    'mime',
    'side-channel',
    'call-bind',
    'get-intrinsic',
    'has-symbols',
    'object-inspect',
    'es-errors'
];

let allDependenciesOk = true;

for (const dep of requiredDependencies) {
    try {
        require.resolve(dep);
        console.log(`✅ ${dep} - installed`);
    } catch (error) {
        console.log(`❌ ${dep} - missing`);
        allDependenciesOk = false;
    }
}

console.log('\n' + '='.repeat(50));

if (allDependenciesOk) {
    console.log('✅ All dependencies are correctly installed!');
    console.log('You can run build script to build the application');
} else {
    console.log('❌ Some dependencies are missing!');
    console.log('Please run the build script to install dependencies');
}

console.log('='.repeat(50));

// Additional check: verify critical files exist
console.log('\nVerifying critical files...\n');

const fs = require('fs');
const path = require('path');

const requiredFiles = [
    'main.js',
    'package.json',
    'index.html',
    'InputListener.exe',
    'assets/icon.png'
];

let allFilesOk = true;

for (const file of requiredFiles) {
    if (fs.existsSync(path.join(__dirname, file))) {
        console.log(`✅ ${file} - exists`);
    } else {
        console.log(`❌ ${file} - missing`);
        allFilesOk = false;
    }
}

console.log('\n' + '='.repeat(50));

if (allFilesOk) {
    console.log('✅ All critical files exist!');
} else {
    console.log('❌ Some critical files are missing!');
}

console.log('='.repeat(50));

if (allDependenciesOk && allFilesOk) {
    console.log('\n🎉 Verification passed! Ready to build application.');
} else {
    console.log('\n⚠️  Verification failed! Please check the issues above.');
}
