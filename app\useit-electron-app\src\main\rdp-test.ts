import rdp from 'node-rdpjs-2'
import net from 'net'

export class RdpTest {
  // 测试TCP连接到RDP端口
  static async testTcpConnection(ip: string, port: number = 3389): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log(`=== TCP Connection Test to ${ip}:${port} ===`);
      
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        reject(new Error(`TCP connection timeout to ${ip}:${port}`));
      }, 10000);

      socket.on('connect', () => {
        console.log(`✅ TCP connection successful to ${ip}:${port}`);
        clearTimeout(timeout);
        socket.destroy();
        resolve({
          success: true,
          message: `TCP connection successful to ${ip}:${port}`
        });
      });

      socket.on('error', (err) => {
        console.log(`❌ TCP connection failed to ${ip}:${port}:`, err.message);
        clearTimeout(timeout);
        reject({
          success: false,
          error: err.message
        });
      });

      socket.connect(port, ip);
    });
  }
  
  static async testConnection(ip: string, username: string, password: string): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log('=== RDP Connection Test ===');
      console.log(`Testing connection to: ${ip}:3389`);
      console.log(`Username: ${username}`);
      console.log(`Password: ${'*'.repeat(password.length)}`);
      
      const timeout = setTimeout(() => {
        console.log('Connection test timed out after 30 seconds');
        client.removeAllListeners();
        reject(new Error('Connection timeout'));
      }, 30000);

      // 使用增强配置参数（包含剪贴板和打字支持）
      const client = rdp.createClient({
        domain: '',  // Windows域，本地连接为空
        userName: username,
        password: password,
        enablePerf: true,  // 启用性能优化
        autoLogin: true,   // 自动登录
        screen: { width: 800, height: 600 },
        locale: 'en',      // 键盘布局
        logLevel: 'INFO',  // 与mstsc.js保持一致的日志级别
        // 启用剪贴板和设备重定向
        enableClipboard: true,  // 启用剪贴板共享
        enableLocalPrinters: true,  // 启用本地打印机
        enableSmartCards: true,  // 启用智能卡
        // 音频设置
        audioPlayMode: 'local',  // 本地播放音频
        audioCaptureMode: false,  // 不捕获音频
        // 显示优化设置
        showWallpaper: false,  // 不显示壁纸以提高性能
        fontSmoothing: true,  // 启用字体平滑
        desktopComposition: true,  // 启用桌面合成
        showThemes: true,  // 显示主题
        showBlinkingCursor: true  // 显示闪烁光标
      });

      let events: string[] = [];

      client.on('connect', () => {
        console.log('✅ RDP CONNECT event received');
        events.push('connect');
        
        clearTimeout(timeout);
        client.removeAllListeners();
        
        resolve({
          success: true,
          events,
          message: 'RDP connection test successful'
        });
      });

      client.on('bitmap', (bitmap: any) => {
        console.log(`✅ RDP BITMAP received: ${bitmap.width}x${bitmap.height}`);
        events.push('bitmap');
      });

      client.on('close', () => {
        console.log('❌ RDP CLOSE event received');
        events.push('close');
        
        clearTimeout(timeout);
        
        if (!events.includes('connect')) {
          reject({
            success: false,
            events,
            error: 'Connection closed before connect'
          });
        }
      });

      client.on('error', (err: Error) => {
        console.log('❌ RDP ERROR event:', err.message);
        console.log('Error details:', err);
        events.push('error');
        
        clearTimeout(timeout);
        client.removeAllListeners();
        
        reject({
          success: false,
          events,
          error: err.message,
          errorDetails: err.stack
        });
      });

      try {
        console.log('Calling client.connect() with standard node-rdpjs parameters...');
        client.connect(ip, 3389);
        console.log('client.connect() called, waiting for events...');
      } catch (error) {
        clearTimeout(timeout);
        console.log('❌ Exception calling client.connect():', error);
        reject({
          success: false,
          events,
          error: (error as Error).message
        });
      }
    });
  }
} 