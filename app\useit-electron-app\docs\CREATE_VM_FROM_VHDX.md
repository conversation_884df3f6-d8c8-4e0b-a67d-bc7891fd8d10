# 从VHDX创建虚拟机功能

## 功能概述

新增了从现有VHDX文件创建虚拟机的功能，支持从Hyper-V导出的虚拟机镜像重新创建虚拟机。

## 使用方法

### 1. 准备VHDX文件

- 从Hyper-V管理器导出虚拟机，获得VHDX文件
- 确保VHDX文件路径可访问
- 建议将VHDX文件放在容易访问的位置（如Downloads文件夹）

### 2. 在应用中创建虚拟机

1. 启动UseIt Electron应用
2. 在VM Setup界面，选择"Create from VHDX"选项卡
3. 配置以下参数：
   - **VHDX File Path**: 选择或输入VHDX文件的完整路径
   - **VM Name** (可选): 自定义虚拟机名称，默认为"UseIt-VM-From-VHDX"
   - **Memory (GB)**: 设置虚拟机内存大小，默认2GB
   - **Processors**: 设置虚拟机处理器数量，默认2个
4. 点击"Create VM from VHDX"按钮
5. 系统会打开管理员PowerShell窗口执行创建过程

### 3. 创建过程

脚本会执行以下操作：
- 验证VHDX文件存在性和格式
- 检查Hyper-V环境
- 创建VM目录
- 复制VHDX文件到VM目录
- 创建虚拟机并配置设置
- 连接网络适配器
- 配置启动顺序（从硬盘启动）

## 技术实现

### 新增文件

1. **PowerShell脚本**: `src/resources/scripts/create-vm-from-vhdx.ps1`
   - 处理从VHDX创建虚拟机的核心逻辑
   - 支持参数化配置
   - 包含错误处理和验证

2. **API接口**: 
   - 主进程: `create-vm-from-vhdx` IPC处理器
   - 预加载: `createVmFromVhdx` 方法
   - 前端: 新的UI组件和状态管理

### 功能特点

- **文件验证**: 自动验证VHDX文件格式和存在性
- **参数化配置**: 支持自定义VM名称、内存、处理器等
- **错误处理**: 完善的错误提示和异常处理
- **用户友好**: 直观的文件选择器和配置界面
- **安全性**: 管理员权限执行，确保Hyper-V操作权限

## 与原有功能的区别

| 功能 | Create from ISO | Create from VHDX |
|------|----------------|------------------|
| 用途 | 全新安装Windows | 导入现有虚拟机 |
| 启动方式 | 从DVD/ISO启动 | 从硬盘启动 |
| 安装过程 | 需要手动安装Windows | 直接使用现有系统 |
| 适用场景 | 创建新环境 | 迁移或复制现有环境 |

## 注意事项

1. **管理员权限**: 创建虚拟机需要管理员权限
2. **Hyper-V要求**: 确保Hyper-V已启用并正确配置
3. **磁盘空间**: 确保有足够空间复制VHDX文件
4. **网络配置**: 使用"Default Switch"网络交换机
5. **文件路径**: 避免使用包含特殊字符的路径

## 故障排除

### 常见错误

1. **"VHDX file not found"**: 检查文件路径是否正确
2. **"Network switch not found"**: 确保Hyper-V网络配置正确
3. **"VM already exists"**: 删除同名虚拟机或使用不同名称
4. **权限错误**: 确保以管理员身份运行

### 调试方法

- 查看PowerShell窗口的详细输出
- 检查Hyper-V管理器中的虚拟机状态
- 验证VHDX文件完整性
