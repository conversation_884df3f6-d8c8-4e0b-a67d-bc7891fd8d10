#!/usr/bin/env python3
"""
测试脚本：用于测试新增的文件传输和命令执行API接口
"""

import requests
import base64
import json
import os
import tempfile

# API基础URL - 使用主程序端口3000
BASE_URL = "http://localhost:3000"

def test_upload_file():
    """测试文件上传接口"""
    print("\n=== 测试文件上传接口 ===")
    
    try:
        # 创建一个测试文件
        test_content = "这是一个测试文件\nTest file content\n测试时间: " + str(os.times())
        test_content_bytes = test_content.encode('utf-8')
        
        # 编码为base64
        file_data_b64 = base64.b64encode(test_content_bytes).decode('utf-8')
        
        # 准备上传数据
        upload_data = {
            "file_data": file_data_b64,
            "file_path": os.path.join(os.path.expanduser('~'), 'Downloads', 'test_upload.txt'),
            "filename": "test_upload.txt"
        }
        
        # 发送请求
        response = requests.post(
            f"{BASE_URL}/uploadFile",
            json=upload_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                # 验证文件是否真的被创建
                file_path = result['file_path']
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"✅ 文件上传成功，内容验证: {content[:50]}...")
                    return True
                else:
                    print("❌ 文件上传失败：文件未创建")
                    return False
            else:
                print(f"❌ 文件上传失败: {result['message']}")
                return False
        else:
            print(f"❌ 文件上传失败，HTTP状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 文件上传接口测试失败: {e}")
        return False

def test_upload_file_to_directory():
    """测试上传文件到目录"""
    print("\n=== 测试上传文件到目录 ===")
    
    try:
        # 创建一个测试文件
        test_content = "目录上传测试文件\nDirectory upload test"
        test_content_bytes = test_content.encode('utf-8')
        
        # 编码为base64
        file_data_b64 = base64.b64encode(test_content_bytes).decode('utf-8')
        
        # 准备上传数据 - 使用目录路径
        target_dir = os.path.join(os.path.expanduser('~'), 'Downloads', 'test_dir')
        upload_data = {
            "file_data": file_data_b64,
            "file_path": target_dir,  # 目录路径
            "filename": "dir_test.txt"
        }
        
        # 发送请求
        response = requests.post(
            f"{BASE_URL}/uploadFile",
            json=upload_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        return response.status_code == 200 and response.json().get('status') == 'success'
        
    except Exception as e:
        print(f"❌ 目录上传测试失败: {e}")
        return False

def test_execute_command():
    """测试命令执行接口"""
    print("\n=== 测试命令执行接口 ===")
    
    try:
        # 测试简单命令
        command_data = {
            "command": "echo Hello World && dir",
            "timeout": 10
        }
        
        response = requests.post(
            f"{BASE_URL}/executeCommand",
            json=command_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        if response.status_code == 200 and result['status'] == 'success':
            print(f"✅ 命令执行成功")
            print(f"返回码: {result['return_code']}")
            print(f"标准输出: {result['stdout'][:100]}...")
            if result['stderr']:
                print(f"错误输出: {result['stderr'][:100]}...")
            return True
        else:
            print(f"❌ 命令执行失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 命令执行接口测试失败: {e}")
        return False

def test_execute_command_with_workdir():
    """测试带工作目录的命令执行"""
    print("\n=== 测试带工作目录的命令执行 ===")
    
    try:
        # 使用用户主目录作为工作目录
        work_dir = os.path.expanduser('~')
        command_data = {
            "command": "pwd && ls -la" if os.name != 'nt' else "cd && dir",
            "working_dir": work_dir,
            "timeout": 10
        }
        
        response = requests.post(
            f"{BASE_URL}/executeCommand",
            json=command_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应状态: {result['status']}")
        
        if response.status_code == 200 and result['status'] == 'success':
            print(f"✅ 带工作目录的命令执行成功")
            print(f"工作目录: {result['working_dir']}")
            return True
        else:
            print(f"❌ 带工作目录的命令执行失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 带工作目录的命令执行测试失败: {e}")
        return False

def test_health_check():
    """测试健康检查接口（验证服务是否运行）"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print("健康检查接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("新增API接口测试")
    print("=" * 60)
    
    # 首先测试服务是否运行
    print("\n1. 测试服务状态...")
    if not test_health_check():
        print("❌ 服务未运行或不可用，请先启动listener服务")
        print("提示: 运行 python listener.py 或 python server.py")
        return
    
    print("✅ 服务运行正常")
    
    # 测试文件上传
    print("\n2. 测试文件上传...")
    upload_success = test_upload_file()
    
    # 测试目录上传
    print("\n3. 测试目录上传...")
    dir_upload_success = test_upload_file_to_directory()
    
    # 测试命令执行
    print("\n4. 测试命令执行...")
    command_success = test_execute_command()
    
    # 测试带工作目录的命令执行
    print("\n5. 测试带工作目录的命令执行...")
    workdir_command_success = test_execute_command_with_workdir()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"文件上传接口: {'✅ 通过' if upload_success else '❌ 失败'}")
    print(f"目录上传接口: {'✅ 通过' if dir_upload_success else '❌ 失败'}")
    print(f"命令执行接口: {'✅ 通过' if command_success else '❌ 失败'}")
    print(f"工作目录命令执行: {'✅ 通过' if workdir_command_success else '❌ 失败'}")
    
    all_passed = all([upload_success, dir_upload_success, command_success, workdir_command_success])
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    print("=" * 60)

if __name__ == "__main__":
    main()
