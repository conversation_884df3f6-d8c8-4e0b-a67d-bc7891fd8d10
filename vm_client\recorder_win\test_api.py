#!/usr/bin/env python3
"""
测试脚本：用于测试recorder的显示和隐藏API接口
"""

import requests
import time
import json

# API基础URL
BASE_URL = "http://localhost:3000"

def test_show_window():
    """测试显示窗口接口"""
    try:
        response = requests.post(f"{BASE_URL}/showWindow")
        print("显示窗口接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"显示窗口接口测试失败: {e}")
        return False

def test_hide_window():
    """测试隐藏窗口接口"""
    try:
        response = requests.post(f"{BASE_URL}/hideWindow")
        print("隐藏窗口接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"隐藏窗口接口测试失败: {e}")
        return False

def test_get_status():
    """测试获取状态接口（验证应用是否运行）"""
    try:
        response = requests.get(f"{BASE_URL}/getStatus")
        print("获取状态接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取状态接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Recorder API 接口测试")
    print("=" * 50)
    
    # 首先测试应用是否运行
    print("\n1. 测试应用状态...")
    if not test_get_status():
        print("❌ 应用未运行或API不可用，请先启动recorder应用")
        return
    
    print("✅ 应用运行正常")
    
    # 测试显示窗口
    print("\n2. 测试显示窗口...")
    if test_show_window():
        print("✅ 显示窗口接口工作正常")
    else:
        print("❌ 显示窗口接口测试失败")
    
    # 等待3秒
    print("\n等待3秒...")
    time.sleep(3)
    
    # 测试隐藏窗口
    print("\n3. 测试隐藏窗口...")
    if test_hide_window():
        print("✅ 隐藏窗口接口工作正常")
    else:
        print("❌ 隐藏窗口接口测试失败")
    
    # 等待3秒
    print("\n等待3秒...")
    time.sleep(3)
    
    # 再次显示窗口
    print("\n4. 再次测试显示窗口...")
    if test_show_window():
        print("✅ 再次显示窗口成功")
    else:
        print("❌ 再次显示窗口失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
