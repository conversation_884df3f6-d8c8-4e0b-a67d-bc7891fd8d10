# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all
import os
import sys

datas = []
binaries = []
hiddenimports = []

# 收集基础依赖
tmp_ret = collect_all('pynput')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

# 添加 uiautomation 相关的依赖
tmp_ret = collect_all('uiautomation')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

# 添加 comtypes 相关的依赖 - 精简，只包含核心模块
tmp_ret = collect_all('comtypes')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]

# 添加必要的隐藏导入 - 精简列表，只保留实际使用的模块
hiddenimports += [
    'pynput.keyboard._win32',
    'pynput.mouse._win32',
    'pynput.keyboard._base',
    'pynput.mouse._base',
    'logging',
    'flask',
    'cryptography',
    'win32gui',
    'win32process',
    'psutil',
    'json',
    'datetime',
    'platform',
    'base64',
    'secrets',
    'click',
    'ctypes',
    '_ctypes',
    'comtypes.client',
    'comtypes.automation',
    'uiautomation',
    'waitress',
    'waitress.adjustments',
    'waitress.task',
    'waitress.server',
    'waitress.channel',
    'subprocess',
    'requests',
    'werkzeug',
    'werkzeug.utils',
    'werkzeug.security'
]

# 只添加Python DLL，不包含系统DLL
python_dll = os.path.join(sys.base_prefix, 'python311.dll')
libffi_dll = os.path.join(sys.base_prefix, 'Library', 'bin', 'libffi-8.dll')

# 创建二进制文件列表，只包含必要的文件
binary_files = []

# 添加 Python DLL
if os.path.exists(python_dll):
    binary_files.append((python_dll, '.'))

# 添加 libffi DLL (这个可能是必要的)
if os.path.exists(libffi_dll):
    binary_files.append((libffi_dll, '.'))

# 将收集到的二进制文件添加到现有的二进制文件列表中
binaries.extend(binary_files)

a = Analysis(
    ['server.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 
        'numpy', 
        'pandas', 
        'PyQt5', 
        'tkinter',
        'wx', 
        'PyQt6', 
        'PySide6', 
        'IPython', 
        'scipy', 
        'notebook',
        'pytest', 
        '_pytest'
    ],
    noarchive=False,
    optimize=2  # 提高优化级别
)

# 清理 .pyc 文件
a.datas = [(x, y, z) for (x, y, z) in a.datas if not x.endswith('.pyc')]

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='InputListener',
    debug=False,  # 关闭调试模式
    bootloader_ignore_signals=False,
    strip=False,  # 启用strip减小文件大小
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,  # 启用strip减小文件大小
    upx=True,
    upx_exclude=[],
    name='InputListener',
)