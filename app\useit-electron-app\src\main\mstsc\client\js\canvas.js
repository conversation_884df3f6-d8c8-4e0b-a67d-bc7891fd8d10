/*
 * Copyright (c) 2015 Syl<PERSON><PERSON>te
 *
 * This file is part of mstsc.js.
 *
 * mstsc.js is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

(function() {
	
	/**
	 * decompress bitmap from RLE algorithm
	 * @param	bitmap	{object} bitmap object of bitmap event of node-rdpjs
	 */
	function decompress (bitmap) {
		var fName = null;
		switch (bitmap.bitsPerPixel) {
		case 15:
			fName = 'bitmap_decompress_15';
			break;
		case 16:
			fName = 'bitmap_decompress_16';
			break;
		case 24:
			fName = 'bitmap_decompress_24';
			break;
		case 32:
			fName = 'bitmap_decompress_32';
			break;
		default:
			throw 'invalid bitmap data format';
		}
		
		var input = new Uint8Array(bitmap.data);
		var inputPtr = Module._malloc(input.length);
		var inputHeap = new Uint8Array(Module.HEAPU8.buffer, inputPtr, input.length);
		inputHeap.set(input);
		
		var output_width = bitmap.destRight - bitmap.destLeft + 1;
		var output_height = bitmap.destBottom - bitmap.destTop + 1;
		var ouputSize = output_width * output_height * 4;
		var outputPtr = Module._malloc(ouputSize);

		var outputHeap = new Uint8Array(Module.HEAPU8.buffer, outputPtr, ouputSize);

		var res = Module.ccall(fName,
			'number',
			['number', 'number', 'number', 'number', 'number', 'number', 'number', 'number'],
			[outputHeap.byteOffset, output_width, output_height, bitmap.width, bitmap.height, inputHeap.byteOffset, input.length]
		);
		
		var output = new Uint8ClampedArray(outputHeap.buffer, outputHeap.byteOffset, ouputSize);
		
		Module._free(inputPtr);
		Module._free(outputPtr);
		
		return { width : output_width, height : output_height, data : output };
	}
	
	/**
	 * Un compress bitmap are reverse in y axis
	 */
	function reverse (bitmap) {
		return { width : bitmap.width, height : bitmap.height, data : new Uint8ClampedArray(bitmap.data) };
	}

	/**
	 * High-performance Canvas renderer with advanced optimizations
	 * @param canvas {canvas} use for rendering
	 */
	function Canvas(canvas) {
		this.canvas = canvas;
		this.ctx = canvas.getContext("2d");
		
		// Advanced performance optimizations
		this.pendingUpdates = [];
		this.isUpdateScheduled = false;
		this.lastRenderTime = 0;
		this.targetFPS = 60;
		this.frameInterval = 1000 / this.targetFPS;
		
		// Image data pooling for memory efficiency
		this.imageDataPool = new Map();
		this.maxPoolSize = 10;
		
		// Dirty region tracking for minimal updates
		this.dirtyRegions = [];
		this.fullRedrawThreshold = 50; // If more than 50 regions, do full redraw
		
		// Performance monitoring
		this.renderStats = {
			framesRendered: 0,
			totalRenderTime: 0,
			averageRenderTime: 0
		};
		
		// Initialize Canvas with performance hints
		this.initializeCanvas();
	}
	
	Canvas.prototype = {
		/**
		 * Initialize Canvas with performance optimizations
		 */
		initializeCanvas: function() {
			// Set canvas attributes for better performance
			this.canvas.style.imageRendering = 'pixelated';
			this.canvas.style.imageRendering = '-moz-crisp-edges';
			this.canvas.style.imageRendering = 'crisp-edges';

			// Context optimizations
			this.ctx.imageSmoothingEnabled = false;
			if (this.ctx.webkitImageSmoothingEnabled !== undefined) {
				this.ctx.webkitImageSmoothingEnabled = false;
			}
			if (this.ctx.mozImageSmoothingEnabled !== undefined) {
				this.ctx.mozImageSmoothingEnabled = false;
			}
			if (this.ctx.msImageSmoothingEnabled !== undefined) {
				this.ctx.msImageSmoothingEnabled = false;
			}
		},

		/**
		 * Clear canvas and reset state for session reuse
		 */
		clearAndReset: function() {
			console.log('[Canvas] Clearing canvas and resetting state for session reuse');

			// Save current canvas size
			var canvasWidth = this.canvas.width;
			var canvasHeight = this.canvas.height;

			// Clear the entire canvas with proper context reset
			this.ctx.save();
			this.ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset transform
			this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);

			// Set a neutral background instead of black to avoid confusion
			this.ctx.fillStyle = '#2d2d2d'; // Dark gray instead of black
			this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);
			this.ctx.restore();

			// Reset all rendering state
			this.pendingUpdates = [];
			this.dirtyRegions = [];
			this.isUpdateScheduled = false;
			this.lastRenderTime = 0;

			// Clear image data pool to prevent stale data
			this.imageDataPool.clear();

			// Reset performance stats
			this.renderStats = {
				framesRendered: 0,
				totalRenderTime: 0,
				averageRenderTime: 0
			};

			// Re-initialize canvas settings
			this.initializeCanvas();

			// Force a repaint by triggering canvas update
			this.canvas.style.display = 'none';
			this.canvas.offsetHeight; // Trigger reflow
			this.canvas.style.display = '';

			console.log('[Canvas] Canvas cleared and reset complete, size:', canvasWidth + 'x' + canvasHeight);
		},

		/**
		 * Force full screen refresh - request complete redraw from server
		 */
		requestFullRefresh: function() {
			console.log('[Canvas] Requesting full screen refresh for session reuse');

			// Clear canvas first to show that refresh is happening
			this.clearAndReset();

			// Request refresh from server
			if (this.socket && this.socket.connected) {
				console.log('[Canvas] Emitting refresh-screen event');
				this.socket.emit('refresh-screen');

				// Also try multiple refresh strategies
				var self = this;
				setTimeout(function() {
					if (self.socket && self.socket.connected) {
						console.log('[Canvas] Secondary refresh request');
						self.socket.emit('refresh-screen');
					}
				}, 500);
			} else {
				console.log('[Canvas] Socket not connected, cannot request refresh');
			}
		},

		/**
		 * Force canvas recreation for problematic sessions
		 */
		forceCanvasRecreation: function() {
			console.log('[Canvas] Force recreating canvas context');

			// Save current dimensions
			var width = this.canvas.width;
			var height = this.canvas.height;

			// Force canvas recreation by changing size
			this.canvas.width = width + 1;
			this.canvas.height = height + 1;
			this.canvas.width = width;
			this.canvas.height = height;

			// Get new context
			this.ctx = this.canvas.getContext('2d');

			// Re-initialize
			this.initializeCanvas();

			// Clear with background
			this.ctx.fillStyle = '#2d2d2d';
			this.ctx.fillRect(0, 0, width, height);

			console.log('[Canvas] Canvas context recreated');
		},
		
		/**
		 * Get pooled ImageData to reduce GC pressure
		 */
		getPooledImageData: function(width, height) {
			var key = width + 'x' + height;
			var pool = this.imageDataPool.get(key);
			
			if (pool && pool.length > 0) {
				return pool.pop();
			}
			
			return this.ctx.createImageData(width, height);
		},
		
		/**
		 * Return ImageData to pool for reuse
		 */
		returnToPool: function(imageData) {
			var key = imageData.width + 'x' + imageData.height;
			var pool = this.imageDataPool.get(key) || [];
			
			if (pool.length < this.maxPoolSize) {
				// Clear the data for reuse
				var data = imageData.data;
				for (var i = 0; i < data.length; i += 4) {
					data[i] = 0;     // R
					data[i + 1] = 0; // G
					data[i + 2] = 0; // B
					data[i + 3] = 0; // A
				}
				pool.push(imageData);
				this.imageDataPool.set(key, pool);
			}
		},
		
		/**
		 * Update canvas with new bitmap (with advanced optimizations)
		 * @param bitmap {object}
		 */
		update : function (bitmap) {
			// Add to pending updates for batch processing
			this.pendingUpdates.push(bitmap);
			
			// Track dirty region for optimization
			this.dirtyRegions.push({
				left: bitmap.destLeft,
				top: bitmap.destTop,
				width: bitmap.width,
				height: bitmap.height
			});
			
			// Schedule batch update with frame rate limiting
			if (!this.isUpdateScheduled) {
				this.isUpdateScheduled = true;
				this.scheduleRender();
			}
		},
		
		/**
		 * Smart render scheduling with frame rate limiting
		 */
		scheduleRender: function() {
			var now = performance.now();
			var deltaTime = now - this.lastRenderTime;
			
			if (deltaTime >= this.frameInterval) {
				// Render immediately if enough time has passed
				this.batchUpdate();
			} else {
				// Schedule for next frame
				var delay = this.frameInterval - deltaTime;
				setTimeout(function() {
					requestAnimationFrame(this.batchUpdate.bind(this));
				}.bind(this), delay);
			}
		},
		
		/**
		 * Intelligent batch processing with region optimization
		 */
		batchUpdate : function() {
			var startTime = performance.now();
			this.isUpdateScheduled = false;
			this.lastRenderTime = startTime;
			
			if (this.pendingUpdates.length === 0) {
				return;
			}
			
			// Decide between region updates or full redraw
			if (this.dirtyRegions.length > this.fullRedrawThreshold) {
				this.performFullRedraw();
			} else {
				this.performRegionalUpdates();
			}
			
			// Update performance stats
			var renderTime = performance.now() - startTime;
			this.updateRenderStats(renderTime);
			
			// Clear pending data
			this.pendingUpdates = [];
			this.dirtyRegions = [];
		},
		
		/**
		 * Perform optimized regional updates
		 */
		performRegionalUpdates: function() {
			// Group nearby regions to reduce context switching
			var consolidatedRegions = this.consolidateRegions(this.dirtyRegions);
			
			for (var i = 0; i < this.pendingUpdates.length; i++) {
				this.renderBitmapOptimized(this.pendingUpdates[i]);
			}
		},
		
		/**
		 * Perform full canvas redraw for heavy update scenarios
		 */
		performFullRedraw: function() {
			// Save context state
			this.ctx.save();
			
			// Batch all updates
			for (var i = 0; i < this.pendingUpdates.length; i++) {
				this.renderBitmapOptimized(this.pendingUpdates[i]);
			}
			
			// Restore context state
			this.ctx.restore();
		},
		
		/**
		 * Consolidate overlapping dirty regions
		 */
		consolidateRegions: function(regions) {
			// Simple region consolidation (can be enhanced)
			var consolidated = [];
			var processed = new Array(regions.length).fill(false);
			
			for (var i = 0; i < regions.length; i++) {
				if (processed[i]) continue;
				
				var current = regions[i];
				processed[i] = true;
				
				// Look for overlapping regions
				for (var j = i + 1; j < regions.length; j++) {
					if (processed[j]) continue;
					
					var other = regions[j];
					if (this.regionsOverlap(current, other)) {
						current = this.mergeRegions(current, other);
						processed[j] = true;
					}
				}
				
				consolidated.push(current);
			}
			
			return consolidated;
		},
		
		/**
		 * Check if two regions overlap
		 */
		regionsOverlap: function(a, b) {
			return !(a.left + a.width < b.left || 
					 b.left + b.width < a.left || 
					 a.top + a.height < b.top || 
					 b.top + b.height < a.top);
		},
		
		/**
		 * Merge two regions into bounding rectangle
		 */
		mergeRegions: function(a, b) {
			var left = Math.min(a.left, b.left);
			var top = Math.min(a.top, b.top);
			var right = Math.max(a.left + a.width, b.left + b.width);
			var bottom = Math.max(a.top + a.height, b.top + b.height);
			
			return {
				left: left,
				top: top,
				width: right - left,
				height: bottom - top
			};
		},
		
		/**
		 * Optimized bitmap rendering with pooling
		 */
		renderBitmapOptimized: function(bitmap) {
			var output = null;
			if (bitmap.isCompress) {
				output = decompress(bitmap);
			}
			else {
				output = reverse(bitmap);
			}
			
			// Use pooled ImageData
			var imageData = this.getPooledImageData(output.width, output.height);
			imageData.data.set(output.data);
			
			// Render with clipping for performance
			this.ctx.save();
			this.ctx.beginPath();
			this.ctx.rect(bitmap.destLeft, bitmap.destTop, output.width, output.height);
			this.ctx.clip();
			
			this.ctx.putImageData(imageData, bitmap.destLeft, bitmap.destTop);
			
			this.ctx.restore();
			
			// Return to pool
			this.returnToPool(imageData);
		},
		
		/**
		 * Update rendering performance statistics
		 */
		updateRenderStats: function(renderTime) {
			this.renderStats.framesRendered++;
			this.renderStats.totalRenderTime += renderTime;
			this.renderStats.averageRenderTime = this.renderStats.totalRenderTime / this.renderStats.framesRendered;
			
			// Log performance every 100 frames
			if (this.renderStats.framesRendered % 100 === 0) {
				console.log('[Canvas Performance] Avg render time:', 
					this.renderStats.averageRenderTime.toFixed(2) + 'ms',
					'FPS:', (1000 / this.renderStats.averageRenderTime).toFixed(1));
			}
		}
	}
	
	/**
	 * Module export
	 */
	Mstsc.Canvas = {
		create : function (canvas) {
			return new Canvas(canvas);
		}
	}
})();
