#!/usr/bin/env python3
"""
外部API接口测试脚本
测试通过端口3000调用新增的文件传输和命令执行接口
"""

import requests
import base64
import json
import os

# 外部API基础URL - 端口3000
BASE_URL = "http://localhost:3000"

def test_external_upload_file():
    """测试外部文件上传接口（通过3000端口）"""
    print("\n=== 测试外部文件上传接口 (端口3000) ===")
    
    try:
        # 创建一个测试文件
        test_content = "这是通过外部接口上传的测试文件\nExternal API upload test\n时间: " + str(os.times())
        test_content_bytes = test_content.encode('utf-8')
        
        # 编码为base64
        file_data_b64 = base64.b64encode(test_content_bytes).decode('utf-8')
        
        # 准备上传数据
        upload_data = {
            "file_data": file_data_b64,
            "file_path": os.path.join(os.path.expanduser('~'), 'Downloads', 'external_upload_test.txt'),
            "filename": "external_upload_test.txt"
        }
        
        # 发送请求到外部接口
        response = requests.post(
            f"{BASE_URL}/uploadFile",
            json=upload_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                # 验证文件是否真的被创建
                file_path = result['file_path']
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"✅ 外部文件上传成功，内容验证: {content[:50]}...")
                    return True
                else:
                    print("❌ 外部文件上传失败：文件未创建")
                    return False
            else:
                print(f"❌ 外部文件上传失败: {result['message']}")
                return False
        else:
            print(f"❌ 外部文件上传失败，HTTP状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 外部文件上传接口测试失败: {e}")
        return False

def test_external_execute_command():
    """测试外部命令执行接口（通过3000端口）"""
    print("\n=== 测试外部命令执行接口 (端口3000) ===")
    
    try:
        # 测试简单命令
        command_data = {
            "command": "echo External API Command Test && echo Current Time: %time%",
            "timeout": 10
        }
        
        response = requests.post(
            f"{BASE_URL}/executeCommand",
            json=command_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应状态: {result['status']}")
        
        if response.status_code == 200 and result['status'] == 'success':
            print(f"✅ 外部命令执行成功")
            print(f"返回码: {result['return_code']}")
            print(f"标准输出: {result['stdout'][:100]}...")
            if result['stderr']:
                print(f"错误输出: {result['stderr'][:100]}...")
            return True
        else:
            print(f"❌ 外部命令执行失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 外部命令执行接口测试失败: {e}")
        return False

def test_service_availability():
    """测试服务可用性"""
    print("\n=== 测试服务可用性 ===")
    
    try:
        # 测试主程序是否运行（端口3000）
        response = requests.get(f"{BASE_URL}/getStatus", timeout=5)
        print(f"主程序服务 (端口3000): {'✅ 可用' if response.status_code == 200 else '❌ 不可用'}")
        return response.status_code == 200
    except Exception as e:
        print(f"主程序服务 (端口3000): ❌ 不可用 - {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("外部API接口测试 (通过端口3000)")
    print("=" * 70)
    
    # 首先测试服务是否运行
    print("\n1. 测试服务可用性...")
    if not test_service_availability():
        print("❌ 主程序服务不可用，请先启动Electron应用")
        print("提示: 运行 npm start 或直接启动应用程序")
        return
    
    print("✅ 主程序服务运行正常")
    
    # 测试外部文件上传
    print("\n2. 测试外部文件上传...")
    upload_success = test_external_upload_file()
    
    # 测试外部命令执行
    print("\n3. 测试外部命令执行...")
    command_success = test_external_execute_command()
    
    # 总结
    print("\n" + "=" * 70)
    print("外部API测试结果总结:")
    print(f"文件上传接口 (3000→4000): {'✅ 通过' if upload_success else '❌ 失败'}")
    print(f"命令执行接口 (3000→4000): {'✅ 通过' if command_success else '❌ 失败'}")
    
    all_passed = all([upload_success, command_success])
    print(f"\n总体结果: {'✅ 所有外部接口测试通过' if all_passed else '❌ 部分外部接口测试失败'}")
    
    if all_passed:
        print("\n🎉 恭喜！新增的API接口已正确实现并可通过外部端口3000访问")
        print("📝 接口架构: 外部请求(3000) → 主程序转发 → Python后端(4000)")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
